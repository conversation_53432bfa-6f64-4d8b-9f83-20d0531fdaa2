import sqlite3
import json
from datetime import datetime, timedelta
from collections import Counter, defaultdict
import os
from pathlib import Path

class RecipeDBAnalyzer:
    def __init__(self, db_path):
        """Initialize the analyzer with database connection."""
        self.db_path = db_path
        self.conn = None
        self.connect()

    def connect(self):
        """Establish connection to the database."""
        try:
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            print(f"✓ Connected to database: {self.db_path}")
        except sqlite3.Error as e:
            print(f"✗ Error connecting to database: {e}")
            raise

    def close(self):
        """Close database connection."""
        if self.conn:
            self.conn.close()

    def get_basic_stats(self):
        """Get basic statistics about the database."""
        cursor = self.conn.cursor()

        stats = {}

        # Count recipes
        cursor.execute("SELECT COUNT(*) FROM recipes")
        stats['total_recipes'] = cursor.fetchone()[0]

        # Count favorites
        cursor.execute("SELECT COUNT(*) FROM recipes WHERE is_favorite = 1")
        stats['favorite_recipes'] = cursor.fetchone()[0]

        # Count recipes with ratings
        cursor.execute("SELECT COUNT(*) FROM recipes WHERE rating IS NOT NULL")
        stats['rated_recipes'] = cursor.fetchone()[0]

        # Average rating
        cursor.execute("SELECT AVG(rating) FROM recipes WHERE rating IS NOT NULL")
        avg_rating = cursor.fetchone()[0]
        stats['average_rating'] = round(avg_rating, 2) if avg_rating else 0

        # Count ingredients
        cursor.execute("SELECT COUNT(*) FROM ingredients")
        stats['total_ingredients'] = cursor.fetchone()[0]

        # Count pantry items
        cursor.execute("SELECT COUNT(*) FROM pantry_items")
        stats['pantry_items'] = cursor.fetchone()[0]

        # Count collections
        cursor.execute("SELECT COUNT(*) FROM recipe_collections")
        stats['collections'] = cursor.fetchone()[0]

        # Count search history
        cursor.execute("SELECT COUNT(*) FROM search_history")
        stats['search_history_count'] = cursor.fetchone()[0]

        return stats

    def analyze_time_distribution(self):
        """Analyze cooking times across recipes."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT prep_time, cook_time, total_time FROM recipes")

        prep_times = []
        cook_times = []
        total_times = []

        for row in cursor.fetchall():
            # Parse time strings (assuming format like "30 minutes" or "1 hour")
            def parse_time(time_str):
                if not time_str or time_str == 'N/A':
                    return 0
                try:
                    # Simple parsing - can be enhanced
                    if 'hour' in time_str.lower():
                        return int(time_str.split()[0]) * 60
                    elif 'minute' in time_str.lower():
                        return int(time_str.split()[0])
                    else:
                        return 0
                except:
                    return 0

            prep_times.append(parse_time(row['prep_time']))
            cook_times.append(parse_time(row['cook_time']))
            total_times.append(parse_time(row['total_time']))

        # Filter out zeros for meaningful averages
        prep_times = [t for t in prep_times if t > 0]
        cook_times = [t for t in cook_times if t > 0]
        total_times = [t for t in total_times if t > 0]

        return {
            'avg_prep_time': round(sum(prep_times) / len(prep_times), 1) if prep_times else 0,
            'avg_cook_time': round(sum(cook_times) / len(cook_times), 1) if cook_times else 0,
            'avg_total_time': round(sum(total_times) / len(total_times), 1) if total_times else 0,
            'quickest_total': min(total_times) if total_times else 0,
            'longest_total': max(total_times) if total_times else 0
        }

    def get_most_common_tags(self, limit=10):
        """Get the most common tags across all recipes."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT tags FROM recipes")

        all_tags = []
        for row in cursor.fetchall():
            try:
                tags = json.loads(row['tags'])
                all_tags.extend(tags)
            except:
                continue

        tag_counts = Counter(all_tags)
        return tag_counts.most_common(limit)

    def get_difficulty_distribution(self):
        """Get distribution of recipe difficulties."""
        cursor = self.conn.cursor()
        cursor.execute("""
                       SELECT difficulty, COUNT(*) as count
                       FROM recipes
                       WHERE difficulty IS NOT NULL
                       GROUP BY difficulty
                       """)

        return {row['difficulty']: row['count'] for row in cursor.fetchall()}

    def analyze_ingredients_usage(self, limit=20):
        """Analyze most commonly used ingredients."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT ingredients FROM recipes")

        ingredient_counts = Counter()

        for row in cursor.fetchall():
            try:
                ingredients = json.loads(row['ingredients'])
                # Extract ingredient names (assuming they're strings or dicts with 'name' field)
                for ing in ingredients:
                    if isinstance(ing, str):
                        ingredient_counts[ing.lower()] += 1
                    elif isinstance(ing, dict) and 'name' in ing:
                        ingredient_counts[ing['name'].lower()] += 1
            except:
                continue

        return ingredient_counts.most_common(limit)

    def get_collections_summary(self):
        """Get summary of recipe collections."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT name, description, recipe_ids FROM recipe_collections")

        collections = []
        for row in cursor.fetchall():
            try:
                recipe_ids = json.loads(row['recipe_ids'])
                collections.append({
                    'name': row['name'],
                    'description': row['description'] or 'No description',
                    'recipe_count': len(recipe_ids)
                })
            except:
                continue

        return sorted(collections, key=lambda x: x['recipe_count'], reverse=True)

    def analyze_temporal_patterns(self):
        """Analyze when recipes were added."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT date_added FROM recipes")

        dates = []
        for row in cursor.fetchall():
            try:
                date = datetime.fromisoformat(row['date_added'].replace('Z', '+00:00'))
                dates.append(date)
            except:
                continue

        if not dates:
            return {}

        # Group by month
        monthly_counts = defaultdict(int)
        for date in dates:
            month_key = f"{date.year}-{date.month:02d}"
            monthly_counts[month_key] += 1

        # Days of week
        weekday_counts = Counter(date.weekday() for date in dates)
        weekday_names = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']

        return {
            'total_days_collecting': (max(dates) - min(dates)).days,
            'first_recipe_date': min(dates).strftime('%Y-%m-%d'),
            'last_recipe_date': max(dates).strftime('%Y-%m-%d'),
            'most_active_month': max(monthly_counts.items(), key=lambda x: x[1])[0],
            'most_active_weekday': weekday_names[max(weekday_counts.items(), key=lambda x: x[1])[0]]
        }

    def analyze_pantry_optimization(self, top_n=30):
        """Analyze which ingredients enable making complete recipes."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT id, title, ingredients FROM recipes")

        # Build recipe-ingredient mapping
        recipe_ingredients = {}
        ingredient_to_recipes = defaultdict(set)
        all_ingredients = set()

        for row in cursor.fetchall():
            recipe_id = row['id']
            recipe_title = row['title']
            try:
                ingredients = json.loads(row['ingredients'])
                ingredient_names = []

                for ing in ingredients:
                    if isinstance(ing, str):
                        ing_name = ing.lower().strip()
                    elif isinstance(ing, dict) and 'name' in ing:
                        ing_name = ing['name'].lower().strip()
                    else:
                        continue

                    ingredient_names.append(ing_name)
                    all_ingredients.add(ing_name)
                    ingredient_to_recipes[ing_name].add((recipe_id, recipe_title))

                recipe_ingredients[recipe_id] = set(ingredient_names)
            except:
                continue

        # Find optimal ingredient combinations using a greedy approach
        optimal_pantry = self._find_optimal_pantry(recipe_ingredients, list(all_ingredients), top_n)

        # Calculate detailed coverage for different pantry sizes
        coverage_details = self._analyze_complete_recipe_coverage(recipe_ingredients, optimal_pantry)

        return {
            'optimal_ingredients': optimal_pantry,
            'coverage_analysis': coverage_details,
            'total_unique_recipes': len(recipe_ingredients),
            'total_unique_ingredients': len(all_ingredients)
        }

    def _find_optimal_pantry(self, recipe_ingredients, all_ingredients, target_size):
        """Find the optimal set of ingredients that maximizes complete recipes."""
        # Count how many complete recipes each ingredient contributes to
        ingredient_recipe_coverage = {}

        for ingredient in all_ingredients:
            complete_recipes = set()
            for recipe_id, recipe_ings in recipe_ingredients.items():
                if ingredient in recipe_ings:
                    # Check if we could complete this recipe with this ingredient
                    complete_recipes.add(recipe_id)
            ingredient_recipe_coverage[ingredient] = complete_recipes

        # Use a greedy approach to build optimal pantry
        optimal_pantry = []
        covered_recipes = set()
        remaining_ingredients = set(all_ingredients)

        while len(optimal_pantry) < target_size and remaining_ingredients:
            best_ingredient = None
            best_new_recipes = set()
            best_score = -1

            for ingredient in remaining_ingredients:
                # Calculate which new complete recipes this ingredient would enable
                potential_pantry = set(optimal_pantry + [ingredient])
                new_complete_recipes = set()

                for recipe_id, recipe_ings in recipe_ingredients.items():
                    if recipe_id not in covered_recipes:
                        # Check if recipe can be completed with potential pantry
                        if recipe_ings.issubset(potential_pantry):
                            new_complete_recipes.add(recipe_id)

                # Score based on number of new complete recipes enabled
                score = len(new_complete_recipes)

                # Tiebreaker: prefer ingredients that appear in more recipes overall
                if score > best_score or (score == best_score and
                                          len(ingredient_recipe_coverage.get(ingredient, set())) >
                                          len(ingredient_recipe_coverage.get(best_ingredient, set()))):
                    best_ingredient = ingredient
                    best_new_recipes = new_complete_recipes
                    best_score = score

            if best_ingredient and best_score > 0:
                optimal_pantry.append(best_ingredient)
                covered_recipes.update(best_new_recipes)
                remaining_ingredients.remove(best_ingredient)
            else:
                # If no ingredient adds new complete recipes, add by frequency
                freq_sorted = sorted(remaining_ingredients,
                                     key=lambda x: len(ingredient_recipe_coverage.get(x, set())),
                                     reverse=True)
                if freq_sorted:
                    optimal_pantry.append(freq_sorted[0])
                    remaining_ingredients.remove(freq_sorted[0])
                else:
                    break

        # Add metadata to each ingredient
        result = []
        for ing in optimal_pantry:
            # Find which recipes can be completely made with pantry up to this ingredient
            pantry_so_far = set(optimal_pantry[:optimal_pantry.index(ing) + 1])
            complete_recipes = []

            for recipe_id, recipe_ings in recipe_ingredients.items():
                if recipe_ings.issubset(pantry_so_far):
                    # Get recipe title
                    for r_id, r_title in self._get_recipe_titles(recipe_id):
                        complete_recipes.append(r_title)
                        break

            result.append({
                'ingredient': ing,
                'total_recipe_appearances': len(ingredient_recipe_coverage.get(ing, set())),
                'complete_recipes_enabled': len(complete_recipes),
                'sample_complete_recipes': complete_recipes[:3]
            })

        return result

    def _get_recipe_titles(self, recipe_id):
        """Get recipe title by ID."""
        cursor = self.conn.cursor()
        cursor.execute("SELECT title FROM recipes WHERE id = ?", (recipe_id,))
        row = cursor.fetchone()
        if row:
            return [(recipe_id, row['title'])]
        return []

    def _analyze_complete_recipe_coverage(self, recipe_ingredients, optimal_ingredients):
        """Analyze how many complete recipes can be made with different pantry sizes."""
        coverage_analysis = {}

        for pantry_size in [5, 10, 15, 20, 30]:
            if pantry_size > len(optimal_ingredients):
                continue

            # Get ingredient names only
            pantry = set([ing['ingredient'] for ing in optimal_ingredients[:pantry_size]])
            complete_recipes = []
            complete_count = 0

            for recipe_id, ingredients in recipe_ingredients.items():
                if ingredients.issubset(pantry):
                    complete_count += 1
                    # Get recipe title for the first few
                    if len(complete_recipes) < 5:
                        for r_id, r_title in self._get_recipe_titles(recipe_id):
                            complete_recipes.append(r_title)
                            break

            coverage_analysis[f'top_{pantry_size}_ingredients'] = {
                'complete_recipes': complete_count,
                'coverage_percent': round((complete_count / len(recipe_ingredients)) * 100, 1),
                'sample_recipes': complete_recipes
            }

        return coverage_analysis

    def _analyze_ingredient_combinations(self, recipe_ingredients, essential_ingredients):
        """Analyze how many recipes can be made with different combinations of essential ingredients."""
        coverage_analysis = {}

        # Check coverage with different pantry sizes
        for pantry_size in [5, 10, 15]:
            if pantry_size > len(essential_ingredients):
                continue

            pantry = set(essential_ingredients[:pantry_size])
            makeable_recipes = 0
            partially_makeable = 0

            for recipe_id, ingredients in recipe_ingredients.items():
                matching = len(pantry.intersection(ingredients))
                total = len(ingredients)

                if matching == total and total > 0:
                    makeable_recipes += 1
                elif matching >= total * 0.7 and total > 0:  # 70% of ingredients
                    partially_makeable += 1

            coverage_analysis[f'top_{pantry_size}_ingredients'] = {
                'fully_makeable': makeable_recipes,
                'partially_makeable': partially_makeable,
                'coverage_percent': round((makeable_recipes / len(recipe_ingredients)) * 100, 1)
            }

        return coverage_analysis

    def get_pantry_insights(self):
        """Get insights about pantry items."""
        cursor = self.conn.cursor()

        # Total items
        cursor.execute("SELECT COUNT(*) FROM pantry_items")
        total_items = cursor.fetchone()[0]

        # Items expiring soon (within 7 days)
        cursor.execute("""
                       SELECT COUNT(*) FROM pantry_items
                       WHERE expiry_date IS NOT NULL
                         AND date(expiry_date) <= date('now', '+7 days')
                         AND date(expiry_date) >= date('now')
                       """)
        expiring_soon = cursor.fetchone()[0]

        # Expired items
        cursor.execute("""
                       SELECT COUNT(*) FROM pantry_items
                       WHERE expiry_date IS NOT NULL
                         AND date(expiry_date) < date('now')
                       """)
        expired = cursor.fetchone()[0]

        # Storage locations
        cursor.execute("""
                       SELECT location, COUNT(*) as count
                       FROM pantry_items
                       WHERE location IS NOT NULL
                       GROUP BY location
                       """)
        locations = {row['location']: row['count'] for row in cursor.fetchall()}

        return {
            'total_items': total_items,
            'expiring_soon': expiring_soon,
            'expired': expired,
            'storage_locations': locations
        }

    def generate_report(self):
        """Generate a comprehensive report."""
        print("\n" + "="*60)
        print("RECIPE DATABASE INSIGHTS REPORT")
        print("="*60 + "\n")

        # Basic Statistics
        print("📊 BASIC STATISTICS")
        print("-" * 40)
        stats = self.get_basic_stats()
        for key, value in stats.items():
            print(f"• {key.replace('_', ' ').title()}: {value}")

        # Time Analysis
        print("\n⏱️ COOKING TIME ANALYSIS")
        print("-" * 40)
        time_stats = self.analyze_time_distribution()
        print(f"• Average Prep Time: {time_stats['avg_prep_time']} minutes")
        print(f"• Average Cook Time: {time_stats['avg_cook_time']} minutes")
        print(f"• Average Total Time: {time_stats['avg_total_time']} minutes")
        print(f"• Quickest Recipe: {time_stats['quickest_total']} minutes")
        print(f"• Longest Recipe: {time_stats['longest_total']} minutes")

        # Difficulty Distribution
        print("\n📈 DIFFICULTY DISTRIBUTION")
        print("-" * 40)
        difficulties = self.get_difficulty_distribution()
        if difficulties:
            for diff, count in difficulties.items():
                print(f"• {diff}: {count} recipes")
        else:
            print("• No difficulty ratings found")

        # Popular Tags
        print("\n🏷️ MOST POPULAR TAGS")
        print("-" * 40)
        tags = self.get_most_common_tags(10)
        for tag, count in tags:
            print(f"• {tag}: {count} recipes")

        # Common Ingredients
        print("\n🥘 MOST COMMON INGREDIENTS")
        print("-" * 40)
        ingredients = self.analyze_ingredients_usage(15)
        for ing, count in ingredients[:15]:
            print(f"• {ing}: used in {count} recipes")

        # Collections
        print("\n📁 RECIPE COLLECTIONS")
        print("-" * 40)
        collections = self.get_collections_summary()
        if collections:
            for coll in collections[:10]:
                print(f"• {coll['name']}: {coll['recipe_count']} recipes")
        else:
            print("• No collections found")

        # Temporal Patterns
        print("\n📅 TEMPORAL PATTERNS")
        print("-" * 40)
        temporal = self.analyze_temporal_patterns()
        if temporal:
            for key, value in temporal.items():
                print(f"• {key.replace('_', ' ').title()}: {value}")

        # Pantry Insights
        print("\n🥫 PANTRY INSIGHTS")
        print("-" * 40)
        pantry = self.get_pantry_insights()
        print(f"• Total Items: {pantry['total_items']}")
        print(f"• Expiring Soon (7 days): {pantry['expiring_soon']}")
        print(f"• Expired: {pantry['expired']}")
        if pantry['storage_locations']:
            print("• Storage Locations:")
            for loc, count in pantry['storage_locations'].items():
                print(f"  - {loc}: {count} items")

        # Pantry Optimization Analysis
        print("\n🎯 PANTRY OPTIMIZATION ANALYSIS")
        print("-" * 40)
        print("Finding ingredients that enable complete recipes...")
        optimization = self.analyze_pantry_optimization(30)

        print(f"\nTotal recipes analyzed: {optimization['total_unique_recipes']}")
        print(f"Total unique ingredients: {optimization['total_unique_ingredients']}")

        print("\n📊 COMPLETE Recipe Coverage by Pantry Size:")
        print("(Only counting recipes where ALL ingredients are available)")
        for size, coverage in optimization['coverage_analysis'].items():
            print(f"\n{size.replace('_', ' ').title()}:")
            print(f"  • Can make completely: {coverage['complete_recipes']} recipes ({coverage['coverage_percent']}%)")
            if coverage['sample_recipes']:
                print(f"  • Examples: {', '.join(coverage['sample_recipes'][:3])}")

        print("\n🌟 OPTIMAL INGREDIENTS FOR COMPLETE RECIPES:")
        print("(Listed in order of optimization - each adds new complete recipes)")
        print("-" * 60)

        for i, ing_data in enumerate(optimization['optimal_ingredients'][:20], 1):
            print(f"\n{i}. {ing_data['ingredient'].upper()}")
            print(f"   • Appears in: {ing_data['total_recipe_appearances']} recipes total")
            print(f"   • Enables: {ing_data['complete_recipes_enabled']} complete recipes")
            if ing_data['sample_complete_recipes']:
                print(f"   • Example complete recipes: {', '.join(ing_data['sample_complete_recipes'][:3])}")

        print("\n" + "="*60)
        print("END OF REPORT")
        print("="*60)


def pantry_optimization_only():
    """Run only the pantry optimization analysis."""
    db_path = "/Users/<USER>/Library/Application Support/com.justcooked.app/recipes.db"

    if not os.path.exists(db_path):
        print(f"Error: Database not found at {db_path}")
        return

    try:
        analyzer = RecipeDBAnalyzer(db_path)

        print("\n" + "="*70)
        print("🎯 PANTRY OPTIMIZATION - INGREDIENTS FOR COMPLETE RECIPES")
        print("="*70 + "\n")

        optimization = analyzer.analyze_pantry_optimization(50)

        print(f"Total recipes analyzed: {optimization['total_unique_recipes']}")
        print(f"Total unique ingredients: {optimization['total_unique_ingredients']}")

        print("\n📊 COMPLETE RECIPE COVERAGE BY PANTRY SIZE:")
        print("(These are recipes you can make with ALL ingredients available)")
        print("-" * 60)
        for size, coverage in optimization['coverage_analysis'].items():
            print(f"\n{size.replace('_', ' ').title()}:")
            print(f"  • Complete recipes possible: {coverage['complete_recipes']} ({coverage['coverage_percent']}%)")
            if coverage['sample_recipes']:
                print(f"  • Example recipes: ")
                for recipe in coverage['sample_recipes'][:5]:
                    print(f"    - {recipe}")

        print("\n🌟 OPTIMAL PANTRY INGREDIENTS (Top 40)")
        print("Each ingredient maximizes the number of COMPLETE recipes you can make")
        print("-" * 70)

        cumulative_recipes = 0
        for i, ing_data in enumerate(optimization['optimal_ingredients'][:40], 1):
            cumulative_recipes = ing_data['complete_recipes_enabled']
            print(f"\n{i:2d}. {ing_data['ingredient'].upper()}")
            print(f"    • Found in {ing_data['total_recipe_appearances']} recipes total")
            print(f"    • With ingredients 1-{i}, you can make {cumulative_recipes} complete recipes")
            if ing_data['sample_complete_recipes']:
                print(f"    • New recipes enabled: {', '.join(ing_data['sample_complete_recipes'][:2])}")

        print("\n📋 SHOPPING LIST - TOP 20 ESSENTIALS:")
        print("-" * 50)
        print("If you stock these 20 ingredients, you'll maximize complete recipes:\n")
        for i, ing_data in enumerate(optimization['optimal_ingredients'][:20], 1):
            print(f"{i:2d}. {ing_data['ingredient']}")

        analyzer.close()

    except Exception as e:
        print(f"Error analyzing database: {e}")


def main():
    # Database path
    db_path = "/Users/<USER>/Library/Application Support/com.justcooked.app/recipes.db"

    # Check if database exists
    if not os.path.exists(db_path):
        print(f"Error: Database not found at {db_path}")
        return

    try:
        # Create analyzer and generate report
        analyzer = RecipeDBAnalyzer(db_path)
        analyzer.generate_report()

        # Close connection
        analyzer.close()

    except Exception as e:
        print(f"Error analyzing database: {e}")


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "pantry":
        pantry_optimization_only()
    else:
        main()
