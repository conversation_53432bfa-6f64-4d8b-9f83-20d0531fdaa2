import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Box, Typography, Stack, IconButton, <PERSON>lapse, Divider, Chip } from '@mui/material';
import { ExpandMore, ExpandLess, AutoStories } from '@mui/icons-material';
import { DrawnCard } from '../types/Card';
import { colors } from '../theme/muiTheme';
import cardBackImage from '../assets/card-back.png';

interface EnhancedCardProps {
  card: DrawnCard;
  onReveal: (cardId: string) => void;
  position: { x: number; y: number; label: string };
  isDrawing?: boolean;
  isReading?: boolean;
  containerWidth?: number;
}

export const EnhancedCard: React.FC<EnhancedCardProps> = ({
  card,
  onReveal,
  position,
  isDrawing = false,
  isReading = false,
  containerWidth = 1200
}) => {
  const [showGuidance, setShowGuidance] = useState(false);
  const [isFlipping, setIsFlipping] = useState(false);

  const handleClick = async (event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    console.log('Card clicked:', {
      cardId: card.id,
      isRevealed: card.isRevealed,
      isDrawing,
      isFlipping
    });

    if (!card.isRevealed && !isFlipping) {
      setIsFlipping(true);
      console.log('Starting card reveal for:', card.id);

      // Immediate reveal with animation
      onReveal(card.id);

      // Reset flipping state after animation
      setTimeout(() => {
        setIsFlipping(false);
      }, 800);
    }
  };

  const toggleGuidance = () => {
    setShowGuidance(!showGuidance);
  };

  // Responsive card sizing based on container width and reading state
  const getCardSize = () => {
    if (isReading) {
      const baseWidth = Math.min(containerWidth * 0.28, 380);
      return {
        width: { xs: Math.min(baseWidth * 0.7, 280), sm: Math.min(baseWidth * 0.85, 320), md: baseWidth },
        height: { xs: Math.min(baseWidth * 0.7 * 1.5, 420), sm: Math.min(baseWidth * 0.85 * 1.5, 480), md: baseWidth * 1.5 }
      };
    }
    
    return {
      width: { xs: 280, sm: 320, md: 380 },
      height: { xs: 420, sm: 480, md: 570 }
    };
  };

  const cardSize = getCardSize();

  return (
    <Stack
      className="enhanced-card-container"
      spacing={2}
      alignItems="center"
      sx={{
        position: 'relative',
        margin: { xs: 1, md: 1.5 },
        maxWidth: '100%',
      }}
    >
      {/* Card Position Label */}
      <Typography
        className="card-label"
        variant="h6"
        sx={{
          textAlign: 'center',
          fontFamily: '"Cinzel", serif',
          color: colors.featherGold,
          fontWeight: 500,
          fontSize: { xs: '1rem', md: '1.2rem' },
          letterSpacing: '1px',
          textShadow: `0 0 10px ${colors.featherGold}30`,
          mb: 1.5
        }}
      >
        {position.label}
      </Typography>

      {/* Main Card */}
      <Box
        component={motion.div}
        className="enhanced-card"
        onClick={handleClick}
        initial={isDrawing ? { scale: 0.8, opacity: 0, y: 50 } : false}
        animate={isDrawing ? {
          scale: 1,
          opacity: 1,
          y: 0,
          transition: { duration: 1.2, ease: "easeOut" }
        } : {}}
        whileHover={!card.isRevealed && !isDrawing ? { 
          scale: 1.02,
          y: -5,
          transition: { duration: 0.2 }
        } : {}}
        whileTap={!card.isRevealed && !isDrawing ? { scale: 0.98 } : {}}
        sx={{
          ...cardSize,
          perspective: '1200px',
          cursor: !card.isRevealed && !isDrawing ? 'pointer' : 'default',
          position: 'relative',
        }}
      >
        {/* Card Inner Container with 3D Flip */}
        <Box
          component={motion.div}
          className="card-inner"
          animate={{
            rotateY: card.isRevealed ? (card.isReversed ? 180 : 180) : 0,
          }}
          transition={{
            duration: 0.8,
            ease: "easeInOut"
          }}
          sx={{
            position: 'relative',
            width: '100%',
            height: '100%',
            transformStyle: 'preserve-3d',
          }}
        >
          {/* Card Back */}
          <Box
            className="card-face card-back"
            sx={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backfaceVisibility: 'hidden',
              borderRadius: '20px',
              overflow: 'hidden',
              boxShadow: `0 12px 35px rgba(10, 10, 15, 0.6), 0 0 0 3px ${colors.featherGold}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
              background: `linear-gradient(135deg, #2d1b3d 0%, #4a2c5a 50%, #1a1a2e 100%), radial-gradient(circle at center, rgba(212, 175, 55, 0.3) 0%, transparent 70%)`,
              border: `3px solid ${colors.featherGold}`,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignItems: 'center',
              '&::before': {
                content: '"✦ ◊ ✦"',
                position: 'absolute',
                top: '20px',
                left: '50%',
                transform: 'translateX(-50%)',
                color: colors.featherGold,
                fontSize: '1.2rem',
                letterSpacing: '8px',
              },
              '&::after': {
                content: '"✦ ◊ ✦"',
                position: 'absolute',
                bottom: '20px',
                left: '50%',
                transform: 'translateX(-50%) rotate(180deg)',
                color: colors.featherGold,
                fontSize: '1.2rem',
                letterSpacing: '8px',
              },
            }}
          >
            <Box
              component="img"
              src={cardBackImage}
              alt="Card back"
              sx={{
                width: '75%',
                height: '75%',
                objectFit: 'cover',
                borderRadius: '15px',
                filter: 'sepia(30%) saturate(1.3) brightness(0.8) contrast(1.1)',
                border: `2px solid rgba(212, 175, 55, 0.3)`,
              }}
            />
            {!card.isRevealed && (
              <Typography
                className="card-click-hint"
                sx={{
                  position: 'absolute',
                  bottom: '40px',
                  left: '50%',
                  transform: 'translateX(-50%)',
                  fontFamily: '"Lora", serif',
                  color: colors.moonlightSilver,
                  fontSize: { xs: '0.8rem', md: '0.9rem' },
                  fontStyle: 'italic',
                  opacity: 0.9,
                  textShadow: `0 0 8px ${colors.featherGold}30`,
                  animation: 'gentlePulse 2s ease-in-out infinite',
                  '@keyframes gentlePulse': {
                    '0%, 100%': { opacity: 0.7 },
                    '50%': { opacity: 1 },
                  },
                }}
              >
                Click to reveal
              </Typography>
            )}
          </Box>

          {/* Card Front */}
          <Box
            className="card-face card-front"
            sx={{
              position: 'absolute',
              width: '100%',
              height: '100%',
              backfaceVisibility: 'hidden',
              borderRadius: '20px',
              overflow: 'hidden',
              background: `linear-gradient(135deg, ${colors.etherealWhite} 0%, #c9b8d4 100%), radial-gradient(circle at top right, rgba(212, 175, 55, 0.3) 0%, transparent 50%)`,
              color: colors.cosmicVoid,
              transform: card.isReversed ? 'rotateY(180deg) rotate(180deg)' : 'rotateY(180deg)',
              p: 2,
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'space-between',
              border: `2px solid ${colors.featherGold}`,
              boxShadow: `0 12px 35px rgba(10, 10, 15, 0.6), 0 0 0 3px ${colors.featherGold}, inset 0 1px 0 rgba(245, 243, 240, 0.1)`,
            }}
          >
            {/* Card Header */}
            <Box sx={{ textAlign: 'center', mb: 2 }}>
              <Typography
                variant="h5"
                component="h3"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  fontWeight: 600,
                  color: colors.cosmicVoid,
                  fontSize: { xs: '1.1rem', md: '1.3rem' },
                  lineHeight: 1.2,
                  mb: 1,
                }}
              >
                {card.title}
              </Typography>
              
              <Chip
                label={card.suite}
                size="small"
                sx={{
                  background: `linear-gradient(135deg, ${colors.sacredPurple} 0%, ${colors.tribalEarth} 100%)`,
                  color: colors.etherealWhite,
                  fontFamily: '"Lora", serif',
                  fontSize: '0.75rem',
                  fontWeight: 500,
                }}
              />

              {card.isReversed && (
                <Chip
                  label="Reversed"
                  size="small"
                  sx={{
                    ml: 1,
                    background: `linear-gradient(135deg, ${colors.shadowDeep} 0%, ${colors.cosmicVoid} 100%)`,
                    color: colors.moonlightSilver,
                    fontFamily: '"Lora", serif',
                    fontSize: '0.75rem',
                  }}
                />
              )}
            </Box>

            {/* Card Content */}
            <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', justifyContent: 'center' }}>
              <Typography
                variant="body1"
                sx={{
                  fontFamily: '"Lora", serif',
                  fontSize: { xs: '0.85rem', md: '0.95rem' },
                  lineHeight: 1.4,
                  color: colors.cosmicVoid,
                  textAlign: 'center',
                  mb: 2,
                  fontStyle: 'italic',
                }}
              >
                {card.isReversed ? card.reversal_meaning : card.upright_meaning}
              </Typography>

              <Typography
                variant="body2"
                sx={{
                  fontFamily: '"Lora", serif',
                  fontSize: { xs: '0.8rem', md: '0.9rem' },
                  color: colors.tribalEarth,
                  textAlign: 'center',
                  fontWeight: 500,
                  mb: 2,
                }}
              >
                {card.isReversed ? card.reversal_key_interpretation : card.upright_key_interpretation}
              </Typography>

              <Box
                sx={{
                  background: `linear-gradient(135deg, rgba(74, 44, 90, 0.1) 0%, rgba(26, 26, 46, 0.1) 100%)`,
                  borderRadius: '10px',
                  p: 1.5,
                  border: `1px solid ${colors.featherGold}40`,
                }}
              >
                <Typography
                  variant="body2"
                  sx={{
                    fontFamily: '"Lora", serif',
                    fontSize: { xs: '0.75rem', md: '0.85rem' },
                    color: colors.cosmicVoid,
                    textAlign: 'center',
                    fontStyle: 'italic',
                    fontWeight: 500,
                  }}
                >
                  "{card.isReversed ? card.reversal_affirmation : card.upright_affirmation}"
                </Typography>
              </Box>
            </Box>

            {/* Guidance Toggle Button */}
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <IconButton
                onClick={toggleGuidance}
                size="small"
                sx={{
                  background: `linear-gradient(135deg, ${colors.sacredPurple} 0%, ${colors.tribalEarth} 100%)`,
                  color: colors.etherealWhite,
                  '&:hover': {
                    background: `linear-gradient(135deg, ${colors.featherGold} 0%, ${colors.bioluminescentBlue} 100%)`,
                    color: colors.cosmicVoid,
                  },
                }}
              >
                <AutoStories fontSize="small" />
                {showGuidance ? <ExpandLess fontSize="small" /> : <ExpandMore fontSize="small" />}
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Integrated Guidance Panel */}
      <AnimatePresence>
        {showGuidance && card.isRevealed && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            style={{ width: '100%', overflow: 'hidden' }}
          >
            <Box
              sx={{
                background: `linear-gradient(135deg, rgba(74, 44, 90, 0.4) 0%, rgba(26, 26, 46, 0.6) 100%)`,
                border: `2px solid ${colors.featherGold}`,
                borderRadius: '15px',
                p: 3,
                mt: 2,
                backdropFilter: 'blur(10px)',
                maxWidth: cardSize.width,
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontFamily: '"Cinzel", serif',
                  color: colors.featherGold,
                  mb: 2,
                  textAlign: 'center',
                }}
              >
                Guidance & Reflection
              </Typography>
              
              <Divider sx={{ mb: 2, borderColor: colors.featherGold + '40' }} />
              
              <Typography
                variant="body2"
                sx={{
                  fontFamily: '"Lora", serif',
                  color: colors.etherealWhite,
                  mb: 2,
                  fontSize: '0.9rem',
                  lineHeight: 1.5,
                }}
              >
                <strong>Journal Prompt:</strong><br />
                {card.isReversed ? card.reversal_journal_prompt : card.upright_journal_prompt}
              </Typography>
            </Box>
          </motion.div>
        )}
      </AnimatePresence>
    </Stack>
  );
};
