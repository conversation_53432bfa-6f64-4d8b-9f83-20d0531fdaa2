import React from 'react';
import { motion } from 'framer-motion';
import { DrawnCard } from '../types/Card';
import cardBackImage from '../assets/card-back.png';

interface CardProps {
  card: DrawnCard;
  onReveal: (cardId: string) => void;
  position: { x: number; y: number; label: string };
  isDrawing?: boolean;
}

export const Card: React.FC<CardProps> = ({ 
  card, 
  onReveal, 
  position, 
  isDrawing = false 
}) => {
  const handleClick = () => {
    if (!card.isRevealed && !isDrawing) {
      onReveal(card.id);
    }
  };

  return (
    <div className="card-container" style={{ 
      transform: `translate(${position.x}px, ${position.y}px)` 
    }}>
      <div className="card-label">{position.label}</div>
      <motion.div
        className="card"
        onClick={handleClick}
        initial={isDrawing ? { x: 0, y: 0, scale: 0.8 } : false}
        animate={isDrawing ? { 
          x: position.x, 
          y: position.y, 
          scale: 1,
          transition: { duration: 1, ease: "easeInOut" }
        } : {}}
        whileHover={!card.isRevealed ? { scale: 1.05 } : {}}
        whileTap={!card.isRevealed ? { scale: 0.95 } : {}}
      >
        <motion.div
          className="card-inner"
          initial={false}
          animate={{ rotateY: card.isRevealed ? 180 : 0 }}
          transition={{ duration: 0.6, ease: "easeInOut" }}
        >
          {/* Card Back */}
          <div className="card-face card-back">
            <img src={cardBackImage} alt="Card back" />
            {!card.isRevealed && (
              <div className="card-click-hint">Click to reveal</div>
            )}
          </div>
          
          {/* Card Front */}
          <div className="card-face card-front">
            <div className="card-content">
              <h3 className="card-title">{card.title}</h3>
              <div className="card-suite">{card.suite}</div>
              <div className="card-meaning">
                <p>{card.poetic_meaning}</p>
              </div>
              <div className="card-affirmation">
                <em>"{card.affirmation}"</em>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};
