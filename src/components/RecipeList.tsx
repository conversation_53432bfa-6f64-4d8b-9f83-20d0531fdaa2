import React, { useState } from 'react';
import { Grid, TextField, InputAdornment, Box, Typography } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import RecipeCard from './RecipeCard';
import RecipeDetailDialog from './RecipeDetailDialog';
import { Recipe } from '../models/Recipe';

interface RecipeListProps {
  recipes: Recipe[];
  onRecipeSelect?: (recipe: Recipe) => void;
}

const RecipeList: React.FC<RecipeListProps> = ({ recipes, onRecipeSelect }) => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null);

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleSelectRecipe = (recipe: Recipe) => {
    if (onRecipeSelect) {
      onRecipeSelect(recipe);
    } else {
      setSelectedRecipe(recipe);
    }
  };

  const handleCloseDialog = () => {
    setSelectedRecipe(null);
  };

  // Filter recipes based on search term
  const filteredRecipes = recipes.filter((recipe) => {
    const searchTermLower = searchTerm.toLowerCase();

    return (
      recipe.name.toLowerCase().includes(searchTermLower) ||
      recipe.description.toLowerCase().includes(searchTermLower) ||
      recipe.tags.some((tag) => tag.toLowerCase().includes(searchTermLower))
    );
  });

  return (
    <Box>
      <Box sx={{ mb: 3 }}>
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Search recipes by name, description or tags..."
          value={searchTerm}
          onChange={handleSearch}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon />
              </InputAdornment>
            ),
          }}
        />
      </Box>

      {filteredRecipes.length === 0 ? (
        <Typography align="center" color="textSecondary" variant="h6" sx={{ mt: 4 }}>
          No recipes found
        </Typography>
      ) : (
        <Grid container spacing={3}>
          {filteredRecipes.map((recipe) => (
            <Grid item key={recipe.id} xs={12} sm={6} md={4} lg={3}>
              <RecipeCard recipe={recipe} onSelect={handleSelectRecipe} />
            </Grid>
          ))}
        </Grid>
      )}

      {selectedRecipe && (
        <RecipeDetailDialog
          open={!!selectedRecipe}
          recipe={selectedRecipe}
          onClose={handleCloseDialog}
        />
      )}
    </Box>
  );
};

export default RecipeList;
