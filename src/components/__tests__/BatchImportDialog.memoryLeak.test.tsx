import React from 'react';
import { render, screen, waitFor, act } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import BatchImportDialog from '../BatchImportDialog';
import { batchImportService } from '@services/batchImport';
import { BatchImportStatus } from '@app-types/batchImport';

// Mock the batch import service
jest.mock('@services/batchImport', () => ({
  batchImportService: {
    startBatchImport: jest.fn(),
    getProgress: jest.fn(),
    cancelBatchImport: jest.fn(),
    setProgressCallback: jest.fn(),
    cleanup: jest.fn(),
    getSuggestedCategoryUrls: jest.fn(() => [
      {
        name: 'Desserts',
        url: 'https://www.allrecipes.com/recipes/79/desserts',
        description: 'Sweet treats and desserts'
      }
    ]),
  },
}));

// Mock logger
jest.mock('@services/loggingService', () => ({
  logger: {
    error: jest.fn(),
    info: jest.fn(),
    warn: jest.fn(),
  },
}));

const mockBatchImportService = batchImportService as jest.Mocked<typeof batchImportService>;

describe('BatchImportDialog Memory Leak Prevention', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should cleanup progress polling interval when component unmounts during import', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    // Mock successful import start
    mockBatchImportService.startBatchImport.mockResolvedValue('test-session-id');
    
    // Mock progress responses
    let progressCallCount = 0;
    mockBatchImportService.getProgress.mockImplementation(() => {
      progressCallCount++;
      return Promise.resolve({
        status: BatchImportStatus.IN_PROGRESS,
        totalRecipes: 100,
        processedRecipes: progressCallCount * 10,
        successfulImports: progressCallCount * 8,
        failedImports: progressCallCount * 2,
        estimatedTimeRemaining: 60,
        currentUrl: 'https://example.com/recipe',
        errors: [],
      });
    });

    const onClose = jest.fn();
    const onImportComplete = jest.fn();

    const { unmount } = render(
      <BatchImportDialog
        open={true}
        onClose={onClose}
        onImportComplete={onImportComplete}
      />
    );

    // Start import
    const urlInput = screen.getByTestId('batch-import-url-input');
    await user.type(urlInput, 'https://www.allrecipes.com/recipes/79/desserts');

    const startButton = screen.getByTestId('batch-import-start-button');
    await user.click(startButton);

    // Wait for import to start
    await waitFor(() => {
      expect(mockBatchImportService.startBatchImport).toHaveBeenCalled();
    });

    // Advance timers to trigger progress polling
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    // Verify progress was fetched
    await waitFor(() => {
      expect(mockBatchImportService.getProgress).toHaveBeenCalled();
    });

    const initialCallCount = progressCallCount;

    // Unmount component while import is in progress
    unmount();

    // Advance timers further
    act(() => {
      jest.advanceTimersByTime(10000); // 10 seconds
    });

    // Progress should not be fetched after unmount
    expect(progressCallCount).toBe(initialCallCount);
  });

  it('should not update state after component unmounts', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    // Mock successful import start
    mockBatchImportService.startBatchImport.mockResolvedValue('test-session-id');
    
    // Mock progress that completes after unmount
    mockBatchImportService.getProgress.mockImplementation(() => {
      return new Promise(resolve => {
        setTimeout(() => {
          resolve({
            status: BatchImportStatus.COMPLETED,
            totalRecipes: 100,
            processedRecipes: 100,
            successfulImports: 95,
            failedImports: 5,
            estimatedTimeRemaining: 0,
            currentUrl: '',
            errors: [],
          });
        }, 3000); // Resolve after 3 seconds
      });
    });

    const onClose = jest.fn();
    const onImportComplete = jest.fn();

    const { unmount } = render(
      <BatchImportDialog
        open={true}
        onClose={onClose}
        onImportComplete={onImportComplete}
      />
    );

    // Start import
    const urlInput = screen.getByTestId('batch-import-url-input');
    await user.type(urlInput, 'https://www.allrecipes.com/recipes/79/desserts');

    const startButton = screen.getByTestId('batch-import-start-button');
    await user.click(startButton);

    // Wait for import to start
    await waitFor(() => {
      expect(mockBatchImportService.startBatchImport).toHaveBeenCalled();
    });

    // Advance timers to trigger progress polling
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    // Unmount component before progress resolves
    unmount();

    // Advance timers to let the progress promise resolve
    act(() => {
      jest.advanceTimersByTime(5000);
    });

    // onImportComplete should not be called since component was unmounted
    expect(onImportComplete).not.toHaveBeenCalled();
  });

  it('should handle progress fetch errors gracefully after unmount', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    // Mock successful import start
    mockBatchImportService.startBatchImport.mockResolvedValue('test-session-id');
    
    // Mock progress that throws error
    mockBatchImportService.getProgress.mockRejectedValue(new Error('Network error'));

    const onClose = jest.fn();
    const onImportComplete = jest.fn();

    const { unmount } = render(
      <BatchImportDialog
        open={true}
        onClose={onClose}
        onImportComplete={onImportComplete}
      />
    );

    // Start import
    const urlInput = screen.getByTestId('batch-import-url-input');
    await user.type(urlInput, 'https://www.allrecipes.com/recipes/79/desserts');

    const startButton = screen.getByTestId('batch-import-start-button');
    await user.click(startButton);

    // Wait for import to start
    await waitFor(() => {
      expect(mockBatchImportService.startBatchImport).toHaveBeenCalled();
    });

    // Unmount component
    unmount();

    // Advance timers to trigger progress polling (which will fail)
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    // Should not throw errors or cause issues
    // The test passing means the error was handled gracefully
  });

  it('should clear intervals when switching between import states', async () => {
    const user = userEvent.setup({ advanceTimers: jest.advanceTimersByTime });
    
    // Mock successful import start
    mockBatchImportService.startBatchImport.mockResolvedValue('test-session-id');
    
    let progressCallCount = 0;
    mockBatchImportService.getProgress.mockImplementation(() => {
      progressCallCount++;
      if (progressCallCount <= 3) {
        return Promise.resolve({
          status: BatchImportStatus.IN_PROGRESS,
          totalRecipes: 100,
          processedRecipes: progressCallCount * 10,
          successfulImports: progressCallCount * 8,
          failedImports: progressCallCount * 2,
          estimatedTimeRemaining: 60,
          currentUrl: 'https://example.com/recipe',
          errors: [],
        });
      } else {
        return Promise.resolve({
          status: BatchImportStatus.COMPLETED,
          totalRecipes: 100,
          processedRecipes: 100,
          successfulImports: 95,
          failedImports: 5,
          estimatedTimeRemaining: 0,
          currentUrl: '',
          errors: [],
        });
      }
    });

    const onClose = jest.fn();
    const onImportComplete = jest.fn();

    render(
      <BatchImportDialog
        open={true}
        onClose={onClose}
        onImportComplete={onImportComplete}
      />
    );

    // Start import
    const urlInput = screen.getByTestId('batch-import-url-input');
    await user.type(urlInput, 'https://www.allrecipes.com/recipes/79/desserts');

    const startButton = screen.getByTestId('batch-import-start-button');
    await user.click(startButton);

    // Wait for import to start
    await waitFor(() => {
      expect(mockBatchImportService.startBatchImport).toHaveBeenCalled();
    });

    // Advance timers to trigger multiple progress polls
    act(() => {
      jest.advanceTimersByTime(8000); // 4 polls at 2-second intervals
    });

    // Wait for completion
    await waitFor(() => {
      expect(onImportComplete).toHaveBeenCalled();
    });

    const completionCallCount = progressCallCount;

    // Advance timers further - no more progress calls should happen
    act(() => {
      jest.advanceTimersByTime(10000);
    });

    expect(progressCallCount).toBe(completionCallCount);
  });
});
