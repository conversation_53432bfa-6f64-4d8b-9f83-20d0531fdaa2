import React from 'react';
import { Button, ButtonProps } from '@mui/material';
import { motion } from 'framer-motion';
import { styled } from '@mui/material/styles';
import { colors } from '../theme/muiTheme';

// Styled MUI Button with mystical effects
const StyledMysticalButton = styled(Button)(() => ({
  fontFamily: '"C<PERSON><PERSON>", "Cormorant Garamond", serif',
  background: `linear-gradient(135deg, ${colors.sacredPurple} 0%, ${colors.tribalEarth} 100%)`,
  color: colors.etherealWhite,
  border: `2px solid ${colors.featherGold}`,
  borderRadius: '15px',
  padding: '12px 30px',
  fontSize: '1.1rem',
  fontWeight: 500,
  letterSpacing: '1px',
  textTransform: 'none',
  position: 'relative',
  overflow: 'hidden',
  boxShadow: `0 4px 15px ${colors.mysticalGlow}`,
  transition: 'all 0.4s ease',
  
  '&::before': {
    content: '""',
    position: 'absolute',
    top: 0,
    left: '-100%',
    width: '100%',
    height: '100%',
    background: `linear-gradient(90deg, transparent, ${colors.mysticalGlow}, transparent)`,
    transition: 'left 0.6s ease',
    zIndex: 1,
  },
  
  '&:hover': {
    background: `linear-gradient(135deg, ${colors.featherGold} 0%, ${colors.bioluminescentBlue} 100%)`,
    color: colors.cosmicVoid,
    boxShadow: `0 0 30px ${colors.mysticalGlow}, 0 8px 25px ${colors.mysticalGlow}`,
    transform: 'translateY(-3px)',
    
    '&::before': {
      left: '100%',
    },
  },
  
  '&:disabled': {
    background: `linear-gradient(135deg, ${colors.shadowDeep} 0%, ${colors.cosmicVoid} 100%)`,
    color: colors.moonlightSilver,
    border: `2px solid ${colors.moonlightSilver}`,
    opacity: 0.6,
  },
  
  // Ensure text is above the shimmer effect
  '& .MuiButton-label, & > span': {
    position: 'relative',
    zIndex: 2,
  },
}));

interface MysticalButtonProps extends ButtonProps {
  children: React.ReactNode;
  whileHover?: any;
  whileTap?: any;
  animate?: any;
  initial?: any;
  exit?: any;
  transition?: any;
}

export const MysticalButton = React.forwardRef<HTMLButtonElement, MysticalButtonProps>(({
  children,
  whileHover = { scale: 1.05 },
  whileTap = { scale: 0.95 },
  animate,
  initial,
  exit,
  transition,
  ...buttonProps
}, ref) => {
  return (
    <StyledMysticalButton
      ref={ref}
      component={motion.button}
      whileHover={whileHover}
      whileTap={whileTap}
      animate={animate}
      initial={initial}
      exit={exit}
      transition={transition}
      {...(buttonProps as any)}
    >
      {children}
    </StyledMysticalButton>
  );
});
