import { invoke } from '@tauri-apps/api/core';
import { ImportQueueService } from '../importQueue';
import { BatchImportRequest, ImportQueueStatus, ImportQueueTask } from '@app-types';

// Mock Tauri API
jest.mock('@tauri-apps/api/core');
const mockInvoke = invoke as jest.MockedFunction<typeof invoke>;

// Mock logging service
jest.mock('../loggingService', () => ({
  createLogger: jest.fn(() => ({
    info: jest.fn(),
    error: jest.fn(),
    logError: jest.fn(),
    debug: jest.fn(),
  })),
}));

describe('ImportQueueService', () => {
  let queueService: ImportQueueService;

  const mockBatchRequest: BatchImportRequest = {
    startUrl: 'https://allrecipes.com/recipes/desserts/',
    maxRecipes: 10,
    maxDepth: 2,
    existingUrls: ['https://allrecipes.com/recipe/123/existing'],
  };

  const mockQueueTask: ImportQueueTask = {
    id: 'task-123',
    description: 'Import dessert recipes',
    request: mockBatchRequest,
    status: 'pending',
    progress: null,
    addedAt: '2024-01-01T00:00:00Z',
    startedAt: null,
    completedAt: null,
    error: null,
    estimatedTimeRemaining: null,
  };

  beforeEach(() => {
    jest.clearAllMocks();
    queueService = new ImportQueueService();
  });

  describe('Queue Operations', () => {
    it('should add task to queue', async () => {
      mockInvoke.mockResolvedValue('task-123');

      const taskId = await queueService.addToQueue('Test import', mockBatchRequest);

      expect(mockInvoke).toHaveBeenCalledWith('add_to_import_queue', {
        description: 'Test import',
        request: mockBatchRequest,
      });
      expect(taskId).toBe('task-123');
    });

    it('should get queue status', async () => {
      const mockStatus: ImportQueueStatus = {
        tasks: [mockQueueTask],
        isProcessing: false,
        currentTaskId: null,
        totalTasks: 1,
        pendingTasks: 1,
        completedTasks: 0,
        failedTasks: 0,
      };

      mockInvoke.mockResolvedValue(mockStatus);

      const status = await queueService.getQueueStatus();

      expect(mockInvoke).toHaveBeenCalledWith('get_import_queue_status');
      expect(status).toEqual(mockStatus);
    });

    it('should remove task from queue', async () => {
      mockInvoke.mockResolvedValue(undefined);

      await queueService.removeFromQueue('task-123');

      expect(mockInvoke).toHaveBeenCalledWith('remove_from_import_queue', 'task-123');
    });

    it('should get task progress', async () => {
      const mockProgress = {
        status: 'running',
        totalUrls: 100,
        processedUrls: 50,
        importedRecipes: 25,
        skippedRecipes: 15,
        failedRecipes: 10,
        errors: [],
        estimatedTimeRemaining: 300,
      };

      mockInvoke.mockResolvedValue(mockProgress);

      const progress = await queueService.getTaskProgress('task-123');

      expect(mockInvoke).toHaveBeenCalledWith('get_queue_task_progress', 'task-123');
      expect(progress).toEqual(mockProgress);
    });
  });

  describe('Queue Persistence', () => {
    it('should persist queue state to disk', async () => {
      mockInvoke.mockResolvedValue(undefined);

      await queueService.saveQueueState();

      expect(mockInvoke).toHaveBeenCalledWith('save_import_queue_state');
    });

    it('should restore queue state from disk', async () => {
      const mockPersistedState: ImportQueueStatus = {
        tasks: [
          {
            ...mockQueueTask,
            status: 'pending',
          },
          {
            ...mockQueueTask,
            id: 'task-456',
            status: 'running',
          },
        ],
        isProcessing: true,
        currentTaskId: 'task-456',
        totalTasks: 2,
        pendingTasks: 1,
        completedTasks: 0,
        failedTasks: 0,
      };

      mockInvoke.mockResolvedValue(mockPersistedState);

      const restoredState = await queueService.restoreQueueState();

      expect(mockInvoke).toHaveBeenCalledWith('restore_import_queue_state');
      expect(restoredState).toEqual(mockPersistedState);
    });

    it('should handle missing persisted state gracefully', async () => {
      mockInvoke.mockResolvedValue(null);

      const restoredState = await queueService.restoreQueueState();

      expect(restoredState).toBeNull();
    });

    it('should handle persistence errors gracefully', async () => {
      mockInvoke.mockRejectedValue(new Error('Disk write error'));

      await expect(queueService.saveQueueState()).rejects.toThrow('Failed to save queue state');
    });

    it('should handle restoration errors gracefully', async () => {
      mockInvoke.mockRejectedValue(new Error('Disk read error'));

      await expect(queueService.restoreQueueState()).rejects.toThrow('Failed to restore queue state');
    });
  });

  describe('Queue State Management', () => {
    it('should resume incomplete tasks on startup', async () => {
      const incompleteTask: ImportQueueTask = {
        ...mockQueueTask,
        status: 'running',
        startedAt: '2024-01-01T00:00:00Z',
      };

      const mockState: ImportQueueStatus = {
        tasks: [incompleteTask],
        isProcessing: false,
        currentTaskId: null,
        totalTasks: 1,
        pendingTasks: 0,
        completedTasks: 0,
        failedTasks: 0,
      };

      mockInvoke.mockResolvedValue(mockState);

      await queueService.resumeIncompleteImports();

      expect(mockInvoke).toHaveBeenCalledWith('resume_incomplete_imports');
    });

    it('should handle queue initialization on app startup', async () => {
      mockInvoke.mockResolvedValue(undefined);

      await queueService.initializeQueue();

      expect(mockInvoke).toHaveBeenCalledWith('initialize_import_queue');
    });

    it('should clean up completed tasks', async () => {
      mockInvoke.mockResolvedValue(3); // Number of cleaned tasks

      const cleanedCount = await queueService.cleanupCompletedTasks();

      expect(mockInvoke).toHaveBeenCalledWith('cleanup_completed_queue_tasks');
      expect(cleanedCount).toBe(3);
    });
  });

  describe('Error Handling', () => {
    it('should handle add to queue errors', async () => {
      mockInvoke.mockRejectedValue(new Error('Queue is full'));

      await expect(
        queueService.addToQueue('Test import', mockBatchRequest)
      ).rejects.toThrow('Failed to add task to queue: Queue is full');
    });

    it('should handle queue status errors', async () => {
      mockInvoke.mockRejectedValue(new Error('Database error'));

      await expect(queueService.getQueueStatus()).rejects.toThrow('Failed to get queue status');
    });

    it('should handle remove from queue errors', async () => {
      mockInvoke.mockRejectedValue(new Error('Task not found'));

      await expect(queueService.removeFromQueue('invalid-task')).rejects.toThrow('Failed to remove task from queue');
    });

    it('should handle progress retrieval errors', async () => {
      mockInvoke.mockRejectedValue(new Error('Task not found'));

      const progress = await queueService.getTaskProgress('invalid-task');

      expect(progress).toBeNull();
    });
  });

  describe('Task Description Generation', () => {
    it('should generate descriptive task names', () => {
      const description = queueService.getTaskDescription(mockBatchRequest);

      expect(description).toContain('allrecipes.com');
      expect(description).toContain('10 recipes');
      expect(description).toContain('depth 2');
    });

    it('should handle requests without limits', () => {
      const unlimitedRequest: BatchImportRequest = {
        startUrl: 'https://allrecipes.com/recipes/main-dish/',
        existingUrls: [],
      };

      const description = queueService.getTaskDescription(unlimitedRequest);

      expect(description).toContain('allrecipes.com');
      expect(description).toContain('unlimited');
    });

    it('should handle requests with only max recipes', () => {
      const limitedRequest: BatchImportRequest = {
        startUrl: 'https://allrecipes.com/recipes/appetizers/',
        maxRecipes: 50,
        existingUrls: [],
      };

      const description = queueService.getTaskDescription(limitedRequest);

      expect(description).toContain('50 recipes');
      expect(description).not.toContain('depth');
    });
  });

  describe('Progress Tracking', () => {
    it('should start progress tracking', () => {
      const mockCallback = jest.fn();

      queueService.startProgressTracking(mockCallback);

      expect(queueService.isTrackingProgress()).toBe(true);
    });

    it('should stop progress tracking', () => {
      const mockCallback = jest.fn();
      queueService.startProgressTracking(mockCallback);

      queueService.stopProgressTracking();

      expect(queueService.isTrackingProgress()).toBe(false);
    });

    it('should handle progress updates', async () => {
      const mockCallback = jest.fn();
      const mockProgress = {
        status: 'running',
        totalUrls: 100,
        processedUrls: 50,
        importedRecipes: 25,
        skippedRecipes: 15,
        failedRecipes: 10,
        errors: [],
        estimatedTimeRemaining: 300,
      };

      mockInvoke.mockResolvedValue(mockProgress);
      queueService.startProgressTracking(mockCallback);

      // Simulate progress update
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockCallback).toHaveBeenCalledWith(mockProgress);
    });
  });
});
