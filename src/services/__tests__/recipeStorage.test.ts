import { describe, test, expect, jest, beforeEach } from '@jest/globals';
import {
  readTextFile,
  writeTextFile,
  mkdir,
  remove,
  exists,
  BaseDirectory,
} from '@tauri-apps/plugin-fs';
import Database from '@tauri-apps/plugin-sql';
import {
  saveRecipe,
  getAllRecipes,
  getRecipeById,
  deleteRecipe,
} from '../recipeStorage';
import { deleteRecipeImage } from '../imageService';
import { mockRecipe } from '../../__tests__/fixtures/recipes';

// Mock the dependencies
jest.mock('@tauri-apps/plugin-fs');
jest.mock('@tauri-apps/plugin-sql', () => ({
  default: {
    load: jest.fn(),
  },
}));
jest.mock('../imageService');

const mockReadTextFile = readTextFile as jest.MockedFunction<typeof readTextFile>;
const mockWriteTextFile = writeTextFile as jest.MockedFunction<typeof writeTextFile>;
const mockMkdir = mkdir as jest.MockedFunction<typeof mkdir>;
const mockRemove = remove as jest.MockedFunction<typeof remove>;
const mockExists = exists as jest.MockedFunction<typeof exists>;
const mockDeleteRecipeImage = deleteRecipeImage as jest.MockedFunction<typeof deleteRecipeImage>;
const mockDatabaseLoad = Database.load as jest.MockedFunction<typeof Database.load>;

let mockDb: {
  execute: jest.Mock;
  select: jest.Mock;
};

beforeEach(() => {
  jest.clearAllMocks();
  mockDb = {
    execute: jest.fn().mockResolvedValue({ rowsAffected: 1, lastInsertId: undefined }),
    select: jest.fn().mockResolvedValue([]),
  };
  mockDatabaseLoad.mockResolvedValue(mockDb as any);
});

describe('recipeStorage', () => {
  describe('saveRecipe', () => {
    test('should save a new recipe successfully', async () => {
      // Mock migration check to skip migration
      mockDb.select.mockResolvedValueOnce([{ count: 1 }] as any);

      await saveRecipe(mockRecipe);

      expect(mockDb.execute).toHaveBeenCalledWith(
        'INSERT OR REPLACE INTO recipes (id, data) VALUES (?, ?)',
        [mockRecipe.id, JSON.stringify(mockRecipe)]
      );
    });
  });

  describe('getAllRecipes', () => {
    test('should return all recipes', async () => {
      // Mock migration check
      mockDb.select.mockResolvedValueOnce([{ count: 1 }]);

      // Mock the select for recipes
      mockDb.select.mockResolvedValueOnce([{ data: JSON.stringify(mockRecipe) }] as any);

      const recipes = await getAllRecipes();

      expect(recipes).toEqual([mockRecipe]);
      expect(mockDb.select).toHaveBeenCalledWith('SELECT data FROM recipes');
    });
  });

  describe('getRecipeById', () => {
    test('should return recipe when it exists', async () => {
      // Mock migration check
      mockDb.select.mockResolvedValueOnce([{ count: 1 }] as any);

      // Mock the select for the recipe
      mockDb.select.mockResolvedValueOnce([{ data: JSON.stringify(mockRecipe) }] as any);

      const recipe = await getRecipeById(mockRecipe.id);

      expect(recipe).toEqual(mockRecipe);
      expect(mockDb.select).toHaveBeenCalledWith('SELECT data FROM recipes WHERE id = ?', [mockRecipe.id]);
    });

    test('should return null when recipe does not exist', async () => {
      // Mock migration check
      mockDb.select.mockResolvedValueOnce([{ count: 1 }]);

      // Mock no result
      mockDb.select.mockResolvedValueOnce([] as any);

      const recipe = await getRecipeById('non-existent');

      expect(recipe).toBeNull();
    });
  });

  describe('deleteRecipe', () => {
    test('should delete recipe', async () => {
      // Mock migration check
      mockDb.select.mockResolvedValueOnce([{ count: 1 }] as any);

      // Mock getRecipeById select
      mockDb.select.mockResolvedValueOnce([{ data: JSON.stringify(mockRecipe) }] as any);

      // Mock deleteRecipeImage
      mockDeleteRecipeImage.mockResolvedValue();

      // Mock the delete execute
      mockDb.execute.mockResolvedValueOnce({ rowsAffected: 1 } as any);

      await deleteRecipe(mockRecipe.id);

      expect(mockDeleteRecipeImage).toHaveBeenCalledWith(mockRecipe.image);
      expect(mockDb.execute).toHaveBeenCalledWith('DELETE FROM recipes WHERE id = ?', [mockRecipe.id]);
    });
  });
});
