import { useEffect } from 'react';
import { useAppDispatch, useAppSelector } from '@/store';
import { checkMonitoringStatus, getClipboardHistory } from '@/store/slices/clipboardSlice';

/**
 * Hook to manage clipboard monitoring initialization and status checking
 */
export const useClipboardMonitoring = () => {
  const dispatch = useAppDispatch();
  const { isMonitoring } = useAppSelector((state) => state.clipboard);

  useEffect(() => {
    // Check initial monitoring status when the app starts
    const initializeMonitoring = async () => {
      try {
        await dispatch(checkMonitoringStatus()).unwrap();
        // Also load the initial clipboard history
        await dispatch(getClipboardHistory()).unwrap();
      } catch (error) {
        console.error('Failed to initialize clipboard monitoring:', error);
      }
    };

    initializeMonitoring();
  }, [dispatch]);

  // Periodically refresh clipboard history when monitoring is active
  useEffect(() => {
    if (!isMonitoring) return;

    const interval = setInterval(async () => {
      try {
        await dispatch(getClipboardHistory()).unwrap();
      } catch (error) {
        console.error('Failed to refresh clipboard history:', error);
      }
    }, 2000); // Refresh every 2 seconds

    return () => clearInterval(interval);
  }, [isMonitoring, dispatch]);

  return {
    isMonitoring,
  };
};
