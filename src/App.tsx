import { Container, Typography, Box, Stack } from '@mui/material';
import { OracleReading } from './components/OracleReading';
import { colors } from './theme/muiTheme';
import "./App.css";

function App() {
  return (
    <Box
      className="app"
      sx={{
        minHeight: '100vh',
        height: '100vh',
        width: '100vw',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        overflow: 'hidden',
        // Full-screen background
        background: `
          radial-gradient(ellipse at top, ${colors.tribalEarth} 0%, ${colors.shadowDeep} 40%, ${colors.cosmicVoid} 100%),
          linear-gradient(135deg, ${colors.cosmicVoid} 0%, ${colors.shadowDeep} 50%, ${colors.tribalEarth} 100%)
        `,
        '&::before': {
          content: '""',
          position: 'fixed',
          top: 0,
          left: 0,
          width: '100%',
          height: '100%',
          backgroundImage: `
            radial-gradient(circle at 20% 30%, ${colors.mysticalGlow} 0%, transparent 50%),
            radial-gradient(circle at 80% 70%, rgba(100, 181, 246, 0.1) 0%, transparent 50%),
            radial-gradient(circle at 40% 80%, ${colors.mysticalGlow} 0%, transparent 30%)
          `,
          pointerEvents: 'none',
          zIndex: 0,
        },
      }}
    >
      {/* Header - Compact for desktop */}
      <Box
        component="header"
        className="app-header"
        sx={{
          textAlign: 'center',
          py: { xs: 2, md: 3 },
          px: 2,
          background: `
            linear-gradient(135deg, ${colors.sacredPurple}40 0%, ${colors.shadowDeep}60 100%),
            radial-gradient(ellipse at center, ${colors.mysticalGlow} 0%, transparent 70%)
          `,
          backdropFilter: 'blur(15px)',
          borderBottom: `2px solid ${colors.featherGold}`,
          boxShadow: `0 4px 20px ${colors.mysticalGlow}`,
          position: 'relative',
          zIndex: 2,
          flexShrink: 0,
          '&::before': {
            content: '"✦"',
            position: 'absolute',
            top: '0.5rem',
            left: '50%',
            transform: 'translateX(-50%)',
            fontSize: '1.2rem',
            color: colors.featherGold,
            opacity: 0.7,
          }
        }}
      >
        <Stack spacing={1} alignItems="center">
          <Typography
            variant="h1"
            component="h1"
            sx={{
              background: `linear-gradient(135deg, ${colors.featherGold} 0%, ${colors.bioluminescentBlue} 50%, ${colors.featherGold} 100%)`,
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              backgroundClip: 'text',
              textShadow: `0 0 30px ${colors.mysticalGlow}`,
              letterSpacing: '2px',
              fontSize: { xs: '1.8rem', md: '2.5rem' }
            }}
          >
            The Descent of Falling Bird
          </Typography>
          <Typography
            variant="h6"
            component="p"
            sx={{
              fontStyle: 'italic',
              color: 'text.secondary',
              opacity: 0.9,
              letterSpacing: '1px',
              fontSize: { xs: '0.9rem', md: '1rem' }
            }}
          >
            Sacred Oracle of Shadow Work & Moonlit Wisdom
          </Typography>
        </Stack>
      </Box>

      {/* Main Content Area - Full height for cards */}
      <Box
        component="main"
        className="app-main"
        sx={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          position: 'relative',
          zIndex: 1,
          minHeight: 0,
          overflow: 'hidden',
          width: '100%',
          height: '100%',
        }}
      >
        <OracleReading />
      </Box>
    </Box>
  );
}

export default App;
