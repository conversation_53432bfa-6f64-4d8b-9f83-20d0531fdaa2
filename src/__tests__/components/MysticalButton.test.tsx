import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { MysticalButton } from '../../components/MysticalButton';
import { fallingBirdTheme } from '../../theme/muiTheme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('MysticalButton Component', () => {
  test('renders button with text', () => {
    renderWithTheme(
      <MysticalButton>Test Button</MysticalButton>
    );

    const button = screen.getByRole('button', { name: 'Test Button' });
    expect(button).toBeInTheDocument();
  });

  test('calls onClick when clicked', () => {
    const mockOnClick = jest.fn();
    
    renderWithTheme(
      <MysticalButton onClick={mockOnClick}>
        Click Me
      </MysticalButton>
    );

    const button = screen.getByRole('button', { name: 'Click Me' });
    fireEvent.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  test('can be disabled', () => {
    const mockOnClick = jest.fn();
    
    renderWithTheme(
      <MysticalButton onClick={mockOnClick} disabled>
        Disabled Button
      </MysticalButton>
    );

    const button = screen.getByRole('button', { name: 'Disabled Button' });
    expect(button).toBeDisabled();
    
    fireEvent.click(button);
    expect(mockOnClick).not.toHaveBeenCalled();
  });

  test('accepts different sizes', () => {
    renderWithTheme(
      <MysticalButton size="large">
        Large Button
      </MysticalButton>
    );

    const button = screen.getByRole('button', { name: 'Large Button' });
    expect(button).toBeInTheDocument();
  });

  test('accepts custom className', () => {
    renderWithTheme(
      <MysticalButton className="custom-class">
        Custom Button
      </MysticalButton>
    );

    const button = screen.getByRole('button', { name: 'Custom Button' });
    expect(button).toHaveClass('custom-class');
  });

  test('forwards ref correctly', () => {
    const ref = React.createRef<HTMLButtonElement>();
    
    renderWithTheme(
      <MysticalButton ref={ref}>
        Ref Button
      </MysticalButton>
    );

    expect(ref.current).toBeInstanceOf(HTMLButtonElement);
  });

  test('applies mystical styling', () => {
    const { container } = renderWithTheme(
      <MysticalButton>
        Mystical Button
      </MysticalButton>
    );

    const button = container.querySelector('button');
    expect(button).toHaveStyle({
      position: 'relative',
    });
  });
});
