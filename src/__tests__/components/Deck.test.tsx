import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Deck } from '../../components/Deck';
import { fallingBirdTheme } from '../../theme/muiTheme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('Deck Component', () => {
  const mockOnShuffle = jest.fn();

  beforeEach(() => {
    mockOnShuffle.mockClear();
  });

  test('renders deck with shuffle button when can shuffle', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
        onShuffle={mockOnShuffle}
        canShuffle={true}
      />
    );

    expect(screen.getByText('Shuffle Deck')).toBeInTheDocument();
  });

  test('calls onShuffle when shuffle button is clicked', () => {
    renderWithTheme(
      <Deck
        cardCount={44}
        isShuffling={false}
        onShuffle={mockOnShuffle}
        canShuffle={true}
      />
    );

    const shuffleButton = screen.getByText('Shuffle Deck');
    fireEvent.click(shuffleButton);

    expect(mockOnShuffle).toHaveBeenCalledTimes(1);
  });

  test('has correct styling and structure', () => {
    const { container } = renderWithTheme(
      <Deck onCardDraw={mockOnCardDraw} />
    );

    const deckElement = container.querySelector('.deck');
    expect(deckElement).toBeInTheDocument();
  });

  test('is accessible', () => {
    renderWithTheme(
      <Deck onCardDraw={mockOnCardDraw} />
    );

    const deckElement = screen.getByTestId('deck');
    expect(deckElement).toHaveAttribute('role', 'button');
    expect(deckElement).toHaveAttribute('tabIndex', '0');
  });

  test('handles keyboard interaction', () => {
    renderWithTheme(
      <Deck onCardDraw={mockOnCardDraw} />
    );

    const deckElement = screen.getByTestId('deck');
    
    // Test Enter key
    fireEvent.keyDown(deckElement, { key: 'Enter', code: 'Enter' });
    expect(mockOnCardDraw).toHaveBeenCalledTimes(1);

    // Test Space key
    fireEvent.keyDown(deckElement, { key: ' ', code: 'Space' });
    expect(mockOnCardDraw).toHaveBeenCalledTimes(2);
  });

  test('does not trigger on other keys', () => {
    renderWithTheme(
      <Deck onCardDraw={mockOnCardDraw} />
    );

    const deckElement = screen.getByTestId('deck');
    
    fireEvent.keyDown(deckElement, { key: 'Escape', code: 'Escape' });
    expect(mockOnCardDraw).not.toHaveBeenCalled();
  });

  test('applies hover effects', () => {
    const { container } = renderWithTheme(
      <Deck onCardDraw={mockOnCardDraw} />
    );

    const deckElement = container.querySelector('.deck');
    expect(deckElement).toHaveStyle({
      cursor: 'pointer',
    });
  });
});
