import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { Card } from '../../components/Card';
import { fallingBirdTheme } from '../../theme/muiTheme';
import { DrawnCard } from '../../types/Card';

const mockCard: DrawnCard = {
  id: 'test-card-1',
  title: 'Test Card',
  description: 'This is a test card description',
  keywords: ['test', 'card'],
  image: 'test-image.jpg',
  isRevealed: false,
};

const mockPosition = {
  x: 100,
  y: 200,
  label: 'Test Position',
};

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('Card Component', () => {
  const mockOnReveal = jest.fn();

  beforeEach(() => {
    mockOnReveal.mockClear();
  });

  test('renders card back when not revealed', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const cardElement = screen.getByTestId('oracle-card');
    expect(cardElement).toBeInTheDocument();
    
    // Should show card back
    const cardBack = screen.getByAltText('Card back');
    expect(cardBack).toBeInTheDocument();
  });

  test('renders card content when revealed', () => {
    const revealedCard = { ...mockCard, isRevealed: true };
    
    renderWithTheme(
      <Card
        card={revealedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    expect(screen.getByText('Test Card')).toBeInTheDocument();
    expect(screen.getByText('This is a test card description')).toBeInTheDocument();
    expect(screen.getByText('test, card')).toBeInTheDocument();
  });

  test('calls onReveal when card is clicked and not revealed', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const cardElement = screen.getByTestId('oracle-card');
    fireEvent.click(cardElement);

    expect(mockOnReveal).toHaveBeenCalledWith('test-card-1');
  });

  test('does not call onReveal when card is already revealed', () => {
    const revealedCard = { ...mockCard, isRevealed: true };
    
    renderWithTheme(
      <Card
        card={revealedCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    const cardElement = screen.getByTestId('oracle-card');
    fireEvent.click(cardElement);

    expect(mockOnReveal).not.toHaveBeenCalled();
  });

  test('applies correct styling for drawing state', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={true}
      />
    );

    const cardElement = screen.getByTestId('oracle-card');
    expect(cardElement).toBeInTheDocument();
  });

  test('displays position label when provided', () => {
    renderWithTheme(
      <Card
        card={mockCard}
        onReveal={mockOnReveal}
        position={mockPosition}
        isDrawing={false}
      />
    );

    expect(screen.getByText('Test Position')).toBeInTheDocument();
  });
});
