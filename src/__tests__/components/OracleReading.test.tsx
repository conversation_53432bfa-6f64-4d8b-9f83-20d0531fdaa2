import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { OracleReading } from '../../components/OracleReading';
import { fallingBirdTheme } from '../../theme/muiTheme';

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
  },
  AnimatePresence: ({ children }: any) => children,
}));

// Mock the card utilities
jest.mock('../../utils/cardUtils', () => ({
  drawCards: jest.fn((deck, count) => {
    return Array.from({ length: count }, (_, i) => ({
      id: `card-${i}`,
      title: `Test Card ${i + 1}`,
      suite: 'Test Suite',
      upright_meaning: `Test meaning ${i + 1}`,
      upright_key_interpretation: `Test interpretation ${i + 1}`,
      upright_affirmation: `Test affirmation ${i + 1}`,
      upright_journal_prompt: `Test prompt ${i + 1}`,
      reversal_key_interpretation: `Test reversal interpretation ${i + 1}`,
      reversal_journal_prompt: `Test reversal prompt ${i + 1}`,
      reversal_meaning: `Test reversal meaning ${i + 1}`,
      reversal_affirmation: `Test reversal affirmation ${i + 1}`,
      isRevealed: false,
      isReversed: false,
      position: i,
      isSelected: false,
      isHovered: false
    }));
  }),
  getCardCount: jest.fn((spreadType) => {
    const counts = { 'single': 1, 'three': 3 };
    return counts[spreadType] || 1;
  }),
  getReadingPositions: jest.fn((spreadType) => {
    const positions = {
      'single': [{ x: 0, y: 0, label: 'Your Card' }],
      'three': [
        { x: -200, y: 0, label: 'Past' },
        { x: 0, y: 0, label: 'Present' },
        { x: 200, y: 0, label: 'Future' }
      ]
    };
    return positions[spreadType] || positions['single'];
  }),
  sleep: jest.fn(() => Promise.resolve()),
}));

// Mock the ImmersiveDeck component
jest.mock('../../components/ImmersiveDeck', () => ({
  ImmersiveDeck: ({
    deckState,
    onCardSelect,
    onSpreadComplete,
    selectedCardCount,
    maxSelections,
    isShuffling
  }: any) => (
    <div data-testid="immersive-deck">
      <div data-testid="deck-state">{deckState}</div>
      <div data-testid="selected-count">{selectedCardCount}</div>
      <div data-testid="max-selections">{maxSelections}</div>
      <div data-testid="is-shuffling">{isShuffling.toString()}</div>
      <button
        data-testid="select-card-0"
        onClick={() => onCardSelect(0)}
      >
        Select Card 0
      </button>
      <button
        data-testid="spread-complete"
        onClick={onSpreadComplete}
      >
        Complete Spread
      </button>
    </div>
  ),
}));

// Mock the EnhancedCard component
jest.mock('../../components/EnhancedCard', () => ({
  EnhancedCard: ({ card, onReveal, position, isDrawing, isReading }: any) => (
    <div data-testid={`enhanced-card-${card.id}`} onClick={() => onReveal(card.id)}>
      <div data-testid="card-title">{card.title}</div>
      <div data-testid="card-position">{position.label}</div>
      <div data-testid="card-revealed">{card.isRevealed.toString()}</div>
      <div data-testid="card-drawing">{isDrawing.toString()}</div>
      <div data-testid="card-reading">{isReading.toString()}</div>
    </div>
  ),
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('OracleReading Component', () => {
  describe('Initial Rendering', () => {
    test('renders question phase initially', () => {
      renderWithTheme(<OracleReading />);

      expect(screen.getByText('Sacred Inquiry')).toBeInTheDocument();
      expect(screen.getByText('Enter Sacred Space')).toBeInTheDocument();
      expect(screen.getByText('Hold your question gently in your heart...')).toBeInTheDocument();
    });
  });

  describe('Phase Transitions', () => {
    test('transitions from question to sacred space phase', async () => {
      renderWithTheme(<OracleReading />);

      // Start in question phase
      expect(screen.getByText('Sacred Inquiry')).toBeInTheDocument();

      // Click "Enter Sacred Space" to go to sacred space phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        expect(screen.getByText('Choose Your Path')).toBeInTheDocument();
        expect(screen.getByText('Single Card')).toBeInTheDocument();
        expect(screen.getByText('Three Card Spread')).toBeInTheDocument();
      });
    });

    test('transitions to deck interaction phase when spread is selected', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to sacred space phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Shuffle the Cosmic Deck')).toBeInTheDocument();
        expect(screen.getByTestId('immersive-deck')).toBeInTheDocument();
      });
    });

    test('transitions to card selection phase after deck spread', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to deck interaction phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      // Simulate deck spread completion
      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      await waitFor(() => {
        expect(screen.getByText('Choose Your Cards')).toBeInTheDocument();
      });
    });
  });

  describe('Card Selection and Drawing', () => {

    test('draws cards when card is selected', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to card selection phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      // Select a card
      await waitFor(() => {
        const selectCardButton = screen.getByTestId('select-card-0');
        fireEvent.click(selectCardButton);
      });

      // Should transition to card drawing phase
      await waitFor(() => {
        expect(screen.getByText('Drawing Your Sacred Cards...')).toBeInTheDocument();
      });

      // Then to card revealing phase
      await waitFor(() => {
        expect(screen.getByText('Your Sacred Cards Await')).toBeInTheDocument();
      }, { timeout: 3000 });
    });

  });

  describe('Card Revealing and Reading', () => {
    test('reveals cards when clicked', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to card revealing phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      await waitFor(() => {
        const selectCardButton = screen.getByTestId('select-card-0');
        fireEvent.click(selectCardButton);
      });

      // Wait for card revealing phase and click on card to reveal
      await waitFor(() => {
        expect(screen.getByText('Your Sacred Cards Await')).toBeInTheDocument();
      }, { timeout: 3000 });

      await waitFor(() => {
        const card = screen.getByTestId('enhanced-card-card-0-0');
        fireEvent.click(card);
      });

      // Should transition to reading phase
      await waitFor(() => {
        expect(screen.getByText('Your Sacred Reading')).toBeInTheDocument();
      });
    });

    test('shows completion options when all cards are revealed', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to reading phase with revealed card
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      await waitFor(() => {
        const selectCardButton = screen.getByTestId('select-card-0');
        fireEvent.click(selectCardButton);
      });

      await waitFor(() => {
        const card = screen.getByTestId('enhanced-card-card-0-0');
        fireEvent.click(card);
      });

      // Check for completion options
      await waitFor(() => {
        expect(screen.getByText('Your reading is complete. Take time to reflect on the sacred messages.')).toBeInTheDocument();
        expect(screen.getByText('Complete Reading')).toBeInTheDocument();
      });
    });

  });

  describe('Reading Completion and Reset', () => {
    test('transitions to complete phase and resets when new reading is requested', async () => {
      renderWithTheme(<OracleReading />);

      // Complete a full reading cycle
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      await waitFor(() => {
        const selectCardButton = screen.getByTestId('select-card-0');
        fireEvent.click(selectCardButton);
      });

      await waitFor(() => {
        const card = screen.getByTestId('enhanced-card-card-0-0');
        fireEvent.click(card);
      });

      // Complete the reading
      await waitFor(() => {
        const completeButton = screen.getByText('Complete Reading');
        fireEvent.click(completeButton);
      });

      // Should show completion phase
      await waitFor(() => {
        expect(screen.getByText('Journey Complete')).toBeInTheDocument();
        expect(screen.getByText('Begin New Journey')).toBeInTheDocument();
      });

      // Click Begin New Journey
      const newJourneyButton = screen.getByText('Begin New Journey');
      fireEvent.click(newJourneyButton);

      // Should be back to question phase
      await waitFor(() => {
        expect(screen.getByText('Sacred Inquiry')).toBeInTheDocument();
      });
    });

  });

  describe('Three Card Spread', () => {
    test('handles three-card spread correctly', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to three card spread
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const threeCardButton = screen.getByText('Three Card Spread');
        fireEvent.click(threeCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      // Check that max selections is 3 for three card spread
      await waitFor(() => {
        expect(screen.getByTestId('max-selections')).toHaveTextContent('3');
      });
    });
  });

  describe('Deck State Management', () => {
    test('manages deck state transitions correctly', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to deck interaction
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      // Should start with stacked deck
      await waitFor(() => {
        expect(screen.getByTestId('deck-state')).toHaveTextContent('stacked');
      });

      // After shuffling, should transition to spreading
      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        expect(screen.getByTestId('deck-state')).toHaveTextContent('spreading');
      });
    });
  });

  describe('Component Integration', () => {
    test('passes correct props to ImmersiveDeck', async () => {
      renderWithTheme(<OracleReading />);

      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        expect(screen.getByTestId('immersive-deck')).toBeInTheDocument();
        expect(screen.getByTestId('selected-count')).toHaveTextContent('0');
        expect(screen.getByTestId('max-selections')).toHaveTextContent('1');
        expect(screen.getByTestId('is-shuffling')).toHaveTextContent('false');
      });
    });

    test('passes correct props to EnhancedCard', async () => {
      renderWithTheme(<OracleReading />);

      // Progress to card revealing phase
      const enterButton = screen.getByText('Enter Sacred Space');
      fireEvent.click(enterButton);

      await waitFor(() => {
        const singleCardButton = screen.getByText('Single Card');
        fireEvent.click(singleCardButton);
      });

      await waitFor(() => {
        const shuffleButton = screen.getByText('Shuffle & Spread Cards');
        fireEvent.click(shuffleButton);
      });

      await waitFor(() => {
        const spreadCompleteButton = screen.getByTestId('spread-complete');
        fireEvent.click(spreadCompleteButton);
      });

      await waitFor(() => {
        const selectCardButton = screen.getByTestId('select-card-0');
        fireEvent.click(selectCardButton);
      });

      await waitFor(() => {
        expect(screen.getByTestId('enhanced-card-card-0')).toBeInTheDocument();
        expect(screen.getByTestId('card-drawing')).toHaveTextContent('true');
        expect(screen.getByTestId('card-reading')).toHaveTextContent('false');
      });
    });
  });
});
