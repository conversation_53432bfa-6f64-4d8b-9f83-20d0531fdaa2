import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { MysticalContainer, MysticalPaper } from '../../components/MysticalContainer';
import { fallingBirdTheme } from '../../theme/muiTheme';

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('MysticalContainer Component', () => {
  test('renders children correctly', () => {
    renderWithTheme(
      <MysticalContainer>
        <div>Test Content</div>
      </MysticalContainer>
    );

    expect(screen.getByText('Test Content')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    renderWithTheme(
      <MysticalContainer className="custom-container">
        <div>Content</div>
      </MysticalContainer>
    );

    const container = screen.getByText('Content').closest('.custom-container');
    expect(container).toBeInTheDocument();
  });

  test('accepts maxWidth prop', () => {
    renderWithTheme(
      <MysticalContainer maxWidth="md">
        <div>Content</div>
      </MysticalContainer>
    );

    expect(screen.getByText('Content')).toBeInTheDocument();
  });

  test('accepts motion props', () => {
    renderWithTheme(
      <MysticalContainer
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
      >
        <div>Animated Content</div>
      </MysticalContainer>
    );

    expect(screen.getByText('Animated Content')).toBeInTheDocument();
  });
});

describe('MysticalPaper Component', () => {
  test('renders children correctly', () => {
    renderWithTheme(
      <MysticalPaper>
        <div>Paper Content</div>
      </MysticalPaper>
    );

    expect(screen.getByText('Paper Content')).toBeInTheDocument();
  });

  test('shows decorator by default', () => {
    const { container } = renderWithTheme(
      <MysticalPaper>
        <div>Content with decorator</div>
      </MysticalPaper>
    );

    // Check for the styled component that has the decorator
    const styledPaper = container.querySelector('[class*="MuiPaper-root"]');
    expect(styledPaper).toBeInTheDocument();

    // The decorator is added via CSS ::before pseudo-element,
    // so we check for the styled component instead
    const computedStyle = window.getComputedStyle(styledPaper!);
    expect(styledPaper).toHaveStyle('position: relative');
  });

  test('hides decorator when showDecorator is false', () => {
    const { container } = renderWithTheme(
      <MysticalPaper showDecorator={false}>
        <div>Content without decorator</div>
      </MysticalPaper>
    );

    // When showDecorator is false, it uses regular Paper component
    const paper = container.querySelector('[class*="MuiPaper-root"]');
    expect(paper).toBeInTheDocument();
    expect(paper).not.toHaveStyle('position: relative');
  });

  test('applies custom className', () => {
    renderWithTheme(
      <MysticalPaper className="custom-paper">
        <div>Paper Content</div>
      </MysticalPaper>
    );

    const paper = screen.getByText('Paper Content').closest('.custom-paper');
    expect(paper).toBeInTheDocument();
  });

  test('accepts motion props', () => {
    renderWithTheme(
      <MysticalPaper
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
      >
        <div>Animated Paper</div>
      </MysticalPaper>
    );

    expect(screen.getByText('Animated Paper')).toBeInTheDocument();
  });

  test('accepts onClick handler', () => {
    const mockOnClick = jest.fn();
    
    renderWithTheme(
      <MysticalPaper onClick={mockOnClick}>
        <div>Clickable Paper</div>
      </MysticalPaper>
    );

    const paper = screen.getByText('Clickable Paper').closest('div');
    if (paper) {
      paper.click();
      expect(mockOnClick).toHaveBeenCalledTimes(1);
    }
  });
});
