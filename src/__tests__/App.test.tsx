import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import App from '../App';
import { fallingBirdTheme } from '../theme/muiTheme';

// Mock the OracleReading component to avoid complex dependencies
jest.mock('../components/OracleReading', () => ({
  OracleReading: () => <div data-testid="oracle-reading">Oracle Reading Component</div>,
}));

const renderWithTheme = (component: React.ReactElement) => {
  return render(
    <ThemeProvider theme={fallingBirdTheme}>
      {component}
    </ThemeProvider>
  );
};

describe('App Component', () => {
  test('renders the main title', () => {
    renderWithTheme(<App />);
    
    const title = screen.getByText('The Descent of Falling Bird');
    expect(title).toBeInTheDocument();
  });

  test('renders the subtitle', () => {
    renderWithTheme(<App />);
    
    const subtitle = screen.getByText('Sacred Oracle of Shadow Work & Moonlit Wisdom');
    expect(subtitle).toBeInTheDocument();
  });

  test('renders the oracle reading component', () => {
    renderWithTheme(<App />);

    // Check for the mocked oracle reading component
    const oracleReading = screen.getByText('Oracle Reading Component');
    expect(oracleReading).toBeInTheDocument();
  });

  test('has correct app structure', () => {
    renderWithTheme(<App />);
    
    // Check for header
    const header = screen.getByRole('banner');
    expect(header).toBeInTheDocument();
    
    // Check for main content
    const main = screen.getByRole('main');
    expect(main).toBeInTheDocument();
  });

  test('applies correct CSS classes', () => {
    const { container } = renderWithTheme(<App />);
    
    const appElement = container.querySelector('.app');
    expect(appElement).toBeInTheDocument();
    
    const headerElement = container.querySelector('.app-header');
    expect(headerElement).toBeInTheDocument();
    
    const mainElement = container.querySelector('.app-main');
    expect(mainElement).toBeInTheDocument();
  });
});
