/**
 * Central Type Exports
 *
 * This file provides a central location to import all types from domain-specific
 * type files. This allows for clean imports and better organization.
 */

// Timer and Time Entry Types
export type {
  TimeEntry,
  EarningsData,
  TimeEntryFormProps,
  CalendarViewProps,
  UseSystemTrayProps,
} from './timer';

// Task Management Types
export type {
  Task,
  TaskFormData,
  TaskManagementProps,
  NewTaskDialogProps,
  UseTaskManagementReturn,
} from './task';

// Payout and Data Annotation Types
export type {
  PayoutTask,
  PayoutEntry,
  PayoutExportData,
  PayoutStats,
  PayoutsViewProps,
  UsePayoutManagementReturn,
} from './payout';

// UI Component Types
export type {
  ButtonVariant,
  ButtonSize,
  ActionButtonProps,
  TimerButtonProps,
  NavigationButtonProps,
} from './ui';

// Form Component Types
export type {
  TaskSelectorProps,
  TimeInputProps,
  CurrencyInputProps,
  FormDialogProps,
  ConfirmDialogProps,
} from './form';

// Table Component Types
export type {
  DataTableProps,
  TableColumn,
  TableRow,
  TimeEntryRowProps,
  TaskRowProps,
  PayoutRowProps,
} from './table';

// Re-export all types for backward compatibility during migration
export * from './timer';
export * from './task';
export * from './payout';
