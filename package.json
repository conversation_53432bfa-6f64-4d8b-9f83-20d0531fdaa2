{"name": "paste-king", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.1.1", "@mui/material": "^7.1.1", "@reduxjs/toolkit": "^2.8.2", "@tauri-apps/api": "^2", "@tauri-apps/plugin-opener": "^2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hotkeys-hook": "^5.1.0", "react-redux": "^9.2.0"}, "devDependencies": {"@tauri-apps/cli": "^2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "jsdom": "^26.1.0", "typescript": "~5.6.2", "vite": "^6.0.3", "vitest": "^3.2.3"}}