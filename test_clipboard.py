#!/usr/bin/env python3
"""
Simple test script to verify global clipboard monitoring.
This script will copy different text to the clipboard to test if the Tauri app detects it.
"""

import time
import subprocess
import sys

def copy_to_clipboard(text):
    """Copy text to system clipboard using pbcopy (macOS)"""
    try:
        process = subprocess.Popen(['pbcopy'], stdin=subprocess.PIPE)
        process.communicate(text.encode('utf-8'))
        return True
    except Exception as e:
        print(f"Error copying to clipboard: {e}")
        return False

def main():
    print("Testing global clipboard monitoring...")
    print("Make sure the Paste King app is running!")
    print("This script will copy different text to the clipboard every 3 seconds.")
    print("Check the app to see if it detects the changes.")
    print("Press Ctrl+C to stop.\n")
    
    test_texts = [
        "Hello from external app! 🎉",
        "Testing global clipboard monitoring",
        "This text was copied from a Python script",
        "Multi-line text\nwith line breaks\nshould also work",
        "Special characters: !@#$%^&*()_+-=[]{}|;':\",./<>?",
        "Unicode test: 🚀 🎯 🔥 ⭐ 💡",
        "Long text: " + "Lorem ipsum dolor sit amet, consectetur adipiscing elit. " * 10,
    ]
    
    try:
        for i, text in enumerate(test_texts):
            print(f"Copying text {i+1}/{len(test_texts)}: {text[:50]}{'...' if len(text) > 50 else ''}")
            
            if copy_to_clipboard(text):
                print("✅ Copied to clipboard successfully")
            else:
                print("❌ Failed to copy to clipboard")
            
            print("Waiting 3 seconds...\n")
            time.sleep(3)
        
        print("Test completed! Check the Paste King app to see if all entries were detected.")
        
    except KeyboardInterrupt:
        print("\nTest stopped by user.")

if __name__ == "__main__":
    if sys.platform != "darwin":
        print("This test script is designed for macOS (uses pbcopy).")
        print("For other platforms, modify the copy_to_clipboard function.")
        sys.exit(1)
    
    main()
