{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 10306386172444932100, "profile": 10450692478023715500, "path": 11335712576157124066, "deps": [[3150220818285335163, "url", false, 4160889985274051647], [4405182208873388884, "http", false, 11433473368524258904], [8008191657135824715, "thiserror", false, 12123822820147445168], [8292277814562636972, "tauri_utils", false, 14949306241403492362], [8319709847752024821, "uuid", false, 10870312543692576527], [8866577183823226611, "http_range", false, 5259691744623288337], [9689903380558560274, "serde", false, 9263995837950754330], [11693073011723388840, "raw_window_handle", false, 17007664158566979902], [13208667028893622512, "rand", false, 7789384298557415725], [14162324460024849578, "build_script_build", false, 10007466974407320391], [15367738274754116744, "serde_json", false, 4955143641647444718]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-d144f826b65d30e2/dep-lib-tauri_runtime", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}