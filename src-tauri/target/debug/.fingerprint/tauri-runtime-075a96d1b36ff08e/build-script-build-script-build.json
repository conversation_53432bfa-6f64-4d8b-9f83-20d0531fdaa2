{"rustc": 6560579996391851404, "features": "[]", "declared_features": "[\"clipboard\", \"devtools\", \"global-shortcut\", \"macos-private-api\", \"system-tray\"]", "target": 5408242616063297496, "profile": 316250661675928978, "path": 800464301047246787, "deps": [], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-075a96d1b36ff08e/dep-build-script-build-script-build", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}