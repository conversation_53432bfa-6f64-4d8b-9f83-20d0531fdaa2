{"rustc": 6560579996391851404, "features": "[\"bundled\", \"bundled_bindings\", \"cc\", \"default\", \"min_sqlite_version_3_6_8\", \"pkg-config\", \"vcpkg\"]", "declared_features": "[\"bindgen\", \"buildtime_bindgen\", \"bundled\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"bundled_bindings\", \"cc\", \"default\", \"in_gecko\", \"min_sqlite_version_3_6_23\", \"min_sqlite_version_3_6_8\", \"min_sqlite_version_3_7_16\", \"min_sqlite_version_3_7_7\", \"openssl-sys\", \"pkg-config\", \"preupdate_hook\", \"session\", \"sqlcipher\", \"unlock_notify\", \"vcpkg\", \"wasm32-wasi-vfs\", \"winsqlite3\", \"with-asan\"]", "target": 2511973346261130195, "profile": 5347358027863023418, "path": 14448227620359684529, "deps": [[17031207854228184515, "build_script_build", false, 4616005009430839973]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/libsqlite3-sys-5b1a8f0b5b03c4eb/dep-lib-libsqlite3_sys", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}