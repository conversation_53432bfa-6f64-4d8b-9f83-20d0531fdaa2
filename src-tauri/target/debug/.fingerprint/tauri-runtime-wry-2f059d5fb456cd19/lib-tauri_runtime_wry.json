{"rustc": 6560579996391851404, "features": "[\"objc-exception\"]", "declared_features": "[\"arboard\", \"clipboard\", \"devtools\", \"dox\", \"global-shortcut\", \"linux-headers\", \"macos-private-api\", \"objc-exception\", \"system-tray\", \"tracing\"]", "target": 1901661049345253480, "profile": 5347358027863023418, "path": 6218859703536542018, "deps": [[8292277814562636972, "tauri_utils", false, 14949306241403492362], [8319709847752024821, "uuid", false, 10870312543692576527], [8391357152270261188, "wry", false, 18130688200012768345], [11693073011723388840, "raw_window_handle", false, 17007664158566979902], [13208667028893622512, "rand", false, 7789384298557415725], [14162324460024849578, "tauri_runtime", false, 16415946997134301379], [16228250612241359704, "build_script_build", false, 13398165307663371284], [16371049426708177877, "cocoa", false, 967353187718036524]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/tauri-runtime-wry-2f059d5fb456cd19/dep-lib-tauri_runtime_wry", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}