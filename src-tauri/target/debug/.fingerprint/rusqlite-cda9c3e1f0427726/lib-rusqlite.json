{"rustc": 6560579996391851404, "features": "[\"bundled\", \"modern_sqlite\"]", "declared_features": "[\"array\", \"backup\", \"blob\", \"buildtime_bindgen\", \"bundled\", \"bundled-full\", \"bundled-sqlcipher\", \"bundled-sqlcipher-vendored-openssl\", \"bundled-windows\", \"chrono\", \"collation\", \"column_decltype\", \"csv\", \"csvtab\", \"extra_check\", \"functions\", \"hooks\", \"i128_blob\", \"in_gecko\", \"limits\", \"load_extension\", \"loadable_extension\", \"modern-full\", \"modern_sqlite\", \"release_memory\", \"rusqlite-macros\", \"serde_json\", \"serialize\", \"series\", \"session\", \"sqlcipher\", \"time\", \"trace\", \"unlock_notify\", \"url\", \"uuid\", \"vtab\", \"wasm32-wasi-vfs\", \"window\", \"winsqlite3\", \"with-asan\"]", "target": 10662205063260755052, "profile": 5347358027863023418, "path": 7487312974087846305, "deps": [[3405817021026194662, "hashlink", false, 6497139840859806842], [3666196340704888985, "smallvec", false, 12301240383850435757], [5510864063823219921, "fallible_streaming_iterator", false, 14736163570094660208], [7896293946984509699, "bitflags", false, 390787459793450437], [11564048663401192931, "libsqlite3_sys", false, 320448044559378050], [12860549049674006569, "fallible_iterator", false, 17757276267146073704]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/rusqlite-cda9c3e1f0427726/dep-lib-rusqlite", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}