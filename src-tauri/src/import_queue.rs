#![allow(non_snake_case)]

use serde::{Deserialize, Serialize};
use std::collections::VecDeque;
use std::sync::{Arc, Mutex};
use tokio::sync::mpsc;
use tracing::{info, error, warn, instrument};
use uuid::Uuid;

use crate::batch_import::{BatchImporter, BatchImportRequest, BatchImportProgress, BatchImportResult};

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ImportQueueTask {
    pub id: String,
    pub description: String,
    pub request: BatchImportRequest,
    pub status: ImportQueueTaskStatus,
    pub progress: Option<BatchImportProgress>,
    pub added_at: String,
    pub started_at: Option<String>,
    pub completed_at: Option<String>,
    pub error: Option<String>,
    pub estimated_time_remaining: Option<u32>,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, PartialEq)]
#[serde(rename_all = "camelCase")]
pub enum ImportQueueTaskStatus {
    Pending,
    Running,
    Completed,
    Failed,
    Cancelled,
}

#[derive(Debu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct ImportQueueStatus {
    pub tasks: Vec<ImportQueueTask>,
    pub current_task_id: Option<String>,
    pub is_processing: bool,
    pub total_pending: u32,
    pub total_completed: u32,
    pub total_failed: u32,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct QueueProgressUpdate {
    pub task_id: String,
    pub progress: BatchImportProgress,
}

pub struct ImportQueue {
    tasks: Arc<Mutex<VecDeque<ImportQueueTask>>>,
    current_task: Arc<Mutex<Option<String>>>,
    is_processing: Arc<Mutex<bool>>,
    task_sender: mpsc::UnboundedSender<String>,
    importers: Arc<Mutex<std::collections::HashMap<String, Arc<BatchImporter>>>>,
}

impl ImportQueue {
    pub fn new() -> Self {
        let (task_sender, mut task_receiver) = mpsc::unbounded_channel::<String>();

        let queue = Self {
            tasks: Arc::new(Mutex::new(VecDeque::new())),
            current_task: Arc::new(Mutex::new(None)),
            is_processing: Arc::new(Mutex::new(false)),
            task_sender,
            importers: Arc::new(Mutex::new(std::collections::HashMap::new())),
        };

        // Note: Background processing will be triggered by Tauri commands
        // We don't start a background processor here because we need the app handle

        queue
    }

    #[instrument(skip_all)]
    pub fn add_task(&self, description: String, request: BatchImportRequest) -> Result<String, String> {
        let task_id = Uuid::new_v4().to_string();
        let now = chrono::Utc::now().to_rfc3339();

        let task = ImportQueueTask {
            id: task_id.clone(),
            description,
            request,
            status: ImportQueueTaskStatus::Pending,
            progress: None,
            added_at: now,
            started_at: None,
            completed_at: None,
            error: None,
            estimated_time_remaining: None,
        };

        {
            let mut tasks = self.tasks.lock().unwrap();
            tasks.push_back(task);
        }

        info!("Added task to import queue: {}", task_id);

        // Trigger processing
        if let Err(e) = self.task_sender.send(task_id.clone()) {
            error!("Failed to trigger queue processing: {}", e);
            return Err("Failed to trigger queue processing".to_string());
        }

        Ok(task_id)
    }

    pub async fn process_queue(&self, app: tauri::AppHandle) {
        Self::process_next_task(
            self.tasks.clone(),
            self.current_task.clone(),
            self.is_processing.clone(),
            self.importers.clone(),
            app,
        ).await;
    }

    pub fn get_status(&self) -> ImportQueueStatus {
        let tasks = self.tasks.lock().unwrap();
        let current_task_id = self.current_task.lock().unwrap().clone();
        let is_processing = *self.is_processing.lock().unwrap();

        let all_tasks: Vec<ImportQueueTask> = tasks.iter().cloned().collect();
        
        let total_pending = all_tasks.iter()
            .filter(|t| t.status == ImportQueueTaskStatus::Pending)
            .count() as u32;
        
        let total_completed = all_tasks.iter()
            .filter(|t| t.status == ImportQueueTaskStatus::Completed)
            .count() as u32;
        
        let total_failed = all_tasks.iter()
            .filter(|t| matches!(t.status, ImportQueueTaskStatus::Failed | ImportQueueTaskStatus::Cancelled))
            .count() as u32;

        ImportQueueStatus {
            tasks: all_tasks,
            current_task_id,
            is_processing,
            total_pending,
            total_completed,
            total_failed,
        }
    }

    pub fn remove_task(&self, task_id: &str) -> Result<(), String> {
        let mut tasks = self.tasks.lock().unwrap();
        
        // Find and remove the task
        let position = tasks.iter().position(|task| task.id == task_id);
        
        if let Some(pos) = position {
            let task = &mut tasks[pos];
            
            // If task is running, cancel it
            if task.status == ImportQueueTaskStatus::Running {
                if let Some(importer) = self.importers.lock().unwrap().get(task_id) {
                    importer.cancel();
                }
                task.status = ImportQueueTaskStatus::Cancelled;
                task.completed_at = Some(chrono::Utc::now().to_rfc3339());
            } else if task.status == ImportQueueTaskStatus::Pending {
                // Remove pending task from queue
                tasks.remove(pos);
                info!("Removed pending task from queue: {}", task_id);
                return Ok(());
            }
        } else {
            return Err("Task not found".to_string());
        }

        info!("Cancelled task: {}", task_id);
        Ok(())
    }

    async fn process_next_task(
        tasks: Arc<Mutex<VecDeque<ImportQueueTask>>>,
        current_task: Arc<Mutex<Option<String>>>,
        is_processing: Arc<Mutex<bool>>,
        importers: Arc<Mutex<std::collections::HashMap<String, Arc<BatchImporter>>>>,
        app_handle: Arc<Mutex<Option<tauri::AppHandle>>>,
    ) {
        // Check if already processing
        {
            let processing = is_processing.lock().unwrap();
            if *processing {
                return;
            }
        }

        // Get next pending task
        let next_task = {
            let mut tasks_guard = tasks.lock().unwrap();
            tasks_guard.iter_mut()
                .find(|task| task.status == ImportQueueTaskStatus::Pending)
                .map(|task| {
                    task.status = ImportQueueTaskStatus::Running;
                    task.started_at = Some(chrono::Utc::now().to_rfc3339());
                    task.clone()
                })
        };

        if let Some(mut task) = next_task {
            info!("Starting queue task: {} - {}", task.id, task.description);
            
            // Set processing state
            {
                *is_processing.lock().unwrap() = true;
                *current_task.lock().unwrap() = Some(task.id.clone());
            }

            // Create and store importer
            let importer = Arc::new(BatchImporter::new());
            {
                let mut importers_guard = importers.lock().unwrap();
                importers_guard.insert(task.id.clone(), importer.clone());
            }

            // Execute the import
            let app = {
                let app_guard = app_handle.lock().unwrap();
                if let Some(app) = app_guard.as_ref() {
                    app.clone()
                } else {
                    error!("App handle not available for task execution: {}", task.id);
                    // Update task status to failed
                    {
                        let mut tasks_guard = tasks.lock().unwrap();
                        if let Some(task_mut) = tasks_guard.iter_mut().find(|t| t.id == task.id) {
                            task_mut.status = ImportQueueTaskStatus::Failed;
                            task_mut.error = Some("App handle not available".to_string());
                            task_mut.completed_at = Some(chrono::Utc::now().to_rfc3339());
                        }
                    }

                    // Reset processing state and continue
                    {
                        *is_processing.lock().unwrap() = false;
                        *current_task.lock().unwrap() = None;
                    }
                    return;
                }
            };

            let result = Self::execute_import_task(importer.clone(), task.request.clone(), app).await;

            // Update task status
            {
                let mut tasks_guard = tasks.lock().unwrap();
                if let Some(task_mut) = tasks_guard.iter_mut().find(|t| t.id == task.id) {
                    task_mut.completed_at = Some(chrono::Utc::now().to_rfc3339());
                    task_mut.progress = Some(importer.get_progress());
                    
                    match result {
                        Ok(_) => {
                            task_mut.status = ImportQueueTaskStatus::Completed;
                            info!("Queue task completed successfully: {}", task.id);
                        }
                        Err(e) => {
                            task_mut.status = ImportQueueTaskStatus::Failed;
                            task_mut.error = Some(e.clone());
                            error!("Queue task failed: {} - {}", task.id, e);
                        }
                    }
                }
            }

            // Clean up
            {
                let mut importers_guard = importers.lock().unwrap();
                importers_guard.remove(&task.id);
            }

            // Reset processing state
            {
                *is_processing.lock().unwrap() = false;
                *current_task.lock().unwrap() = None;
            }

            // Check for more tasks
            let has_pending = {
                let tasks_guard = tasks.lock().unwrap();
                tasks_guard.iter().any(|t| t.status == ImportQueueTaskStatus::Pending)
            };

            if has_pending {
                // Process next task
                let tasks_clone = tasks.clone();
                let current_task_clone = current_task.clone();
                let is_processing_clone = is_processing.clone();
                let importers_clone = importers.clone();
                let app_handle_clone = app_handle.clone();

                tokio::spawn(async move {
                    Self::process_next_task(
                        tasks_clone,
                        current_task_clone,
                        is_processing_clone,
                        importers_clone,
                        app_handle_clone,
                    ).await;
                });
            }
        }
    }

    async fn execute_import_task(
        importer: Arc<BatchImporter>,
        request: BatchImportRequest,
        app: tauri::AppHandle,
    ) -> Result<BatchImportResult, String> {
        importer.start_batch_import(app, request).await
    }

    pub fn get_task_progress(&self, task_id: &str) -> Option<BatchImportProgress> {
        if let Some(importer) = self.importers.lock().unwrap().get(task_id) {
            Some(importer.get_progress())
        } else {
            // Check if task is in completed state
            let tasks = self.tasks.lock().unwrap();
            tasks.iter()
                .find(|task| task.id == task_id)
                .and_then(|task| task.progress.clone())
        }
    }
}
