use arboard::{Clipboard, ImageData};
use log::{debug, error, info, warn};
use std::sync::{Arc, Mutex};
use std::time::Duration;
use tokio::time::interval;
use crate::clipboard::ClipboardManager;

pub struct ClipboardMonitor {
    clipboard: Arc<Mutex<Clipboard>>,
    manager: Arc<ClipboardManager>,
    last_text_content: Arc<Mutex<Option<String>>>,
    last_image_hash: Arc<Mutex<Option<String>>>,
    is_running: Arc<Mutex<bool>>,
    poll_interval: Duration,
}

impl ClipboardMonitor {
    pub fn new(manager: Arc<ClipboardManager>) -> Result<Self, Box<dyn std::error::Error>> {
        let clipboard = Clipboard::new().map_err(|e| {
            error!("Failed to initialize clipboard: {}", e);
            e
        })?;

        Ok(Self {
            clipboard: Arc::new(Mutex::new(clipboard)),
            manager,
            last_text_content: Arc::new(Mutex::new(None)),
            last_image_hash: Arc::new(Mutex::new(None)),
            is_running: Arc::new(Mutex::new(false)),
            poll_interval: Duration::from_millis(500), // Poll every 500ms
        })
    }

    pub fn set_poll_interval(&mut self, interval: Duration) {
        self.poll_interval = interval;
    }

    pub fn is_running(&self) -> bool {
        *self.is_running.lock().unwrap_or_else(|e| {
            warn!("Failed to get monitoring state: {}", e);
            e.into_inner()
        })
    }

    pub async fn start_monitoring(&self) -> Result<(), Box<dyn std::error::Error>> {
        {
            let mut is_running = self.is_running.lock().map_err(|e| {
                error!("Failed to acquire lock for is_running: {}", e);
                e.to_string()
            })?;

            if *is_running {
                info!("Clipboard monitoring is already running");
                return Ok(());
            }

            *is_running = true;
        }

        info!("Starting clipboard monitoring with interval: {:?}", self.poll_interval);
        
        // Set the manager monitoring state
        self.manager.set_monitoring(true)?;

        let clipboard = self.clipboard.clone();
        let manager = self.manager.clone();
        let last_text_content = self.last_text_content.clone();
        let last_image_hash = self.last_image_hash.clone();
        let is_running = self.is_running.clone();
        let poll_interval = self.poll_interval;

        tokio::spawn(async move {
            let mut interval_timer = interval(poll_interval);
            
            loop {
                interval_timer.tick().await;

                // Check if we should stop monitoring
                {
                    let running = is_running.lock().unwrap_or_else(|e| {
                        warn!("Failed to check running state: {}", e);
                        e.into_inner()
                    });
                    
                    if !*running {
                        info!("Stopping clipboard monitoring");
                        break;
                    }
                }

                // Check for clipboard changes
                if let Err(e) = Self::check_clipboard_changes(
                    &clipboard,
                    &manager,
                    &last_text_content,
                    &last_image_hash,
                ).await {
                    error!("Error checking clipboard changes: {}", e);
                }
            }
        });

        Ok(())
    }

    pub fn stop_monitoring(&self) -> Result<(), Box<dyn std::error::Error>> {
        let mut is_running = self.is_running.lock().map_err(|e| {
            error!("Failed to acquire lock for is_running: {}", e);
            e.to_string()
        })?;

        if !*is_running {
            info!("Clipboard monitoring is not running");
            return Ok(());
        }

        *is_running = false;
        self.manager.set_monitoring(false)?;
        info!("Clipboard monitoring stopped");

        Ok(())
    }

    async fn check_clipboard_changes(
        clipboard: &Arc<Mutex<Clipboard>>,
        manager: &Arc<ClipboardManager>,
        last_text_content: &Arc<Mutex<Option<String>>>,
        last_image_hash: &Arc<Mutex<Option<String>>>,
    ) -> Result<(), Box<dyn std::error::Error>> {
        // Try to get text content first
        if let Ok(text_content) = Self::get_clipboard_text(clipboard) {
            if let Some(content) = text_content {
                let mut last_text = last_text_content.lock().map_err(|e| e.to_string())?;
                
                // Check if content has changed
                if last_text.as_ref() != Some(&content) {
                    debug!("New text content detected: {} chars", content.len());
                    
                    // Add to clipboard history
                    if let Err(e) = manager.add_entry(content.clone(), "text".to_string()) {
                        error!("Failed to add text entry to clipboard history: {}", e);
                    } else {
                        info!("Added new text entry to clipboard history");
                    }
                    
                    *last_text = Some(content);
                }
            }
        }

        // Try to get image content
        if let Ok(image_data) = Self::get_clipboard_image(clipboard) {
            if let Some(img) = image_data {
                let image_hash = Self::calculate_image_hash(&img);
                let mut last_hash = last_image_hash.lock().map_err(|e| e.to_string())?;
                
                // Check if image has changed
                if last_hash.as_ref() != Some(&image_hash) {
                    debug!("New image content detected");
                    
                    // Convert image to base64 for storage
                    if let Ok(base64_data) = Self::image_to_base64(&img) {
                        if let Err(e) = manager.add_entry(base64_data, "image".to_string()) {
                            error!("Failed to add image entry to clipboard history: {}", e);
                        } else {
                            info!("Added new image entry to clipboard history");
                        }
                    }
                    
                    *last_hash = Some(image_hash);
                }
            }
        }

        Ok(())
    }

    fn get_clipboard_text(clipboard: &Arc<Mutex<Clipboard>>) -> Result<Option<String>, Box<dyn std::error::Error>> {
        let mut cb = clipboard.lock().map_err(|e| e.to_string())?;
        
        match cb.get_text() {
            Ok(text) => {
                if text.trim().is_empty() {
                    Ok(None)
                } else {
                    Ok(Some(text))
                }
            }
            Err(arboard::Error::ContentNotAvailable) => Ok(None),
            Err(e) => {
                debug!("Failed to get clipboard text: {}", e);
                Ok(None)
            }
        }
    }

    fn get_clipboard_image(clipboard: &Arc<Mutex<Clipboard>>) -> Result<Option<ImageData>, Box<dyn std::error::Error>> {
        let mut cb = clipboard.lock().map_err(|e| e.to_string())?;
        
        match cb.get_image() {
            Ok(image) => Ok(Some(image)),
            Err(arboard::Error::ContentNotAvailable) => Ok(None),
            Err(e) => {
                debug!("Failed to get clipboard image: {}", e);
                Ok(None)
            }
        }
    }

    fn calculate_image_hash(image: &ImageData) -> String {
        use std::collections::hash_map::DefaultHasher;
        use std::hash::{Hash, Hasher};
        
        let mut hasher = DefaultHasher::new();
        image.bytes.hash(&mut hasher);
        image.width.hash(&mut hasher);
        image.height.hash(&mut hasher);
        format!("{:x}", hasher.finish())
    }

    fn image_to_base64(image: &ImageData) -> Result<String, Box<dyn std::error::Error>> {
        use base64::{Engine as _, engine::general_purpose};
        
        // Create a simple PNG-like format for storage
        // In a real implementation, you might want to use the `image` crate to encode properly
        let header = format!("data:image/raw;base64,{}x{}:", image.width, image.height);
        let encoded = general_purpose::STANDARD.encode(&image.bytes);
        Ok(format!("{}{}", header, encoded))
    }
}
