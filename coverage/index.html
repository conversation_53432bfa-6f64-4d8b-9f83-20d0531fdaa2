
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">80.66% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>242/300</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">76.99% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>174/226</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">56.75% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>42/74</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">82.74% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>211/255</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line high'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file high" data-value="src"><a href="src/index.html">src</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="6" class="abs high">6/6</td>
	</tr>

<tr>
	<td class="file medium" data-value="src/components"><a href="src/components/index.html">src/components</a></td>
	<td data-value="73.75" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.75" class="pct medium">73.75%</td>
	<td data-value="221" class="abs medium">163/221</td>
	<td data-value="76.49" class="pct medium">76.49%</td>
	<td data-value="217" class="abs medium">166/217</td>
	<td data-value="47.54" class="pct low">47.54%</td>
	<td data-value="61" class="abs low">29/61</td>
	<td data-value="76.96" class="pct medium">76.96%</td>
	<td data-value="191" class="abs medium">147/191</td>
	</tr>

<tr>
	<td class="file high" data-value="src/theme"><a href="src/theme/index.html">src/theme</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="12" class="abs high">12/12</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="0" class="abs high">0/0</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="1" class="abs high">1/1</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	</tr>

<tr>
	<td class="file high" data-value="src/utils"><a href="src/utils/index.html">src/utils</a></td>
	<td data-value="100" class="pic high">
	<div class="chart"><div class="cover-fill cover-full" style="width: 100%"></div><div class="cover-empty" style="width: 0%"></div></div>
	</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="61" class="abs high">61/61</td>
	<td data-value="88.88" class="pct high">88.88%</td>
	<td data-value="9" class="abs high">8/9</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="11" class="abs high">11/11</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="47" class="abs high">47/47</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-06-08T04:18:50.312Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    